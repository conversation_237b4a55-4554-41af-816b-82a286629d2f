{"name": "adaptive-learning-platform", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" --max-warnings=0", "format": "prettier --write .", "test": "vitest", "test:watch": "vitest --watch", "validate": "node scripts/validate-config.js", "test:config": "vitest src/tests/config-validation.test.ts", "test:flow": "vitest src/tests/flow-connectivity.test.ts"}, "dependencies": {"@types/marked": "^5.0.2", "axios": "^1.9.0", "dotenv": "^16.5.0", "firebase": "^11.9.1", "marked": "^15.0.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "zod": "^3.25.62"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^14.1.2", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jsdom": "^26.1.0", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "typescript": "^5.5.5", "vite": "^5.2.0", "vitest": "^1.5.0"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}