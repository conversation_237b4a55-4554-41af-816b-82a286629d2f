#!/usr/bin/env node

/**
 * Script para validar configuração completa do projeto
 */

import { readFileSync } from 'fs';

console.log('🔍 Validando configuração do projeto AdaptEd...\n');

// Carregar variáveis de ambiente manualmente
const envPath = '.env.local';
try {
  const envContent = readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        process.env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
} catch (error) {
  console.warn('⚠️  Arquivo .env.local não encontrado');
}

const validations = [];
let hasErrors = false;

// Função helper para validação
function validateEnv(name, value, required = true, validator = null) {
  const result = {
    name,
    value: value ? '***' : undefined,
    status: 'unknown'
  };

  if (required && (!value || value === '')) {
    result.status = 'error';
    result.message = 'Variável obrigatória não configurada';
    hasErrors = true;
  } else if (!required && (!value || value === '')) {
    result.status = 'warning';
    result.message = 'Variável opcional não configurada';
  } else if (validator && !validator(value)) {
    result.status = 'error';
    result.message = 'Formato inválido';
    hasErrors = true;
  } else {
    result.status = 'success';
    result.message = 'Configurado corretamente';
  }

  validations.push(result);
  return result;
}

// Validações Firebase
console.log('📱 Firebase Configuration:');
validateEnv('VITE_FIREBASE_API_KEY', process.env.VITE_FIREBASE_API_KEY, true, (v) => v.startsWith('AIza'));
validateEnv('VITE_FIREBASE_AUTH_DOMAIN', process.env.VITE_FIREBASE_AUTH_DOMAIN, true, (v) => v.includes('.firebaseapp.com'));
validateEnv('VITE_FIREBASE_PROJECT_ID', process.env.VITE_FIREBASE_PROJECT_ID, true);
validateEnv('VITE_FIREBASE_STORAGE_BUCKET', process.env.VITE_FIREBASE_STORAGE_BUCKET, true);
validateEnv('VITE_FIREBASE_MESSAGING_SENDER_ID', process.env.VITE_FIREBASE_MESSAGING_SENDER_ID, true, (v) => /^\d+$/.test(v));
validateEnv('VITE_FIREBASE_APP_ID', process.env.VITE_FIREBASE_APP_ID, true, (v) => v.includes(':web:'));

console.log('');

// Validações Flow
console.log('🌊 Flow Configuration:');
validateEnv('VITE_FLOW_BASE_URL', process.env.VITE_FLOW_BASE_URL, false, (v) => v.startsWith('http'));
validateEnv('VITE_FLOW_TENANT', process.env.VITE_FLOW_TENANT, false);
validateEnv('VITE_FLOW_CLIENT_ID', process.env.VITE_FLOW_CLIENT_ID, false, (v) => /^[0-9a-f-]{36}$/i.test(v));
validateEnv('VITE_FLOW_CLIENT_SECRET', process.env.VITE_FLOW_CLIENT_SECRET, false);
validateEnv('VITE_FLOW_APP_TO_ACCESS', process.env.VITE_FLOW_APP_TO_ACCESS, false);

console.log('');

// Imprimir resultados
validations.forEach(v => {
  const icons = {
    success: '✅',
    warning: '⚠️ ',
    error: '❌'
  };
  
  console.log(`${icons[v.status]} ${v.name}: ${v.message}`);
});

console.log('');

// Resumo
const errors = validations.filter(v => v.status === 'error').length;
const warnings = validations.filter(v => v.status === 'warning').length;
const success = validations.filter(v => v.status === 'success').length;

console.log('📊 Resumo:');
console.log(`   ✅ Configurações OK: ${success}`);
console.log(`   ⚠️  Avisos: ${warnings}`);
console.log(`   ❌ Erros: ${errors}`);

console.log('');

if (hasErrors) {
  console.log('❌ Projeto NÃO está pronto para deploy - corrija os erros acima');
  process.exit(1);
} else {
  console.log('🎉 Projeto está PRONTO para deploy!');
  
  if (warnings > 0) {
    console.log('💡 Dicas:');
    console.log('   - Variáveis Flow podem ser configuradas via UI');
    console.log('   - Para testes completos, configure todas as variáveis');
  }
  
  console.log('');
  console.log('🚀 Para fazer deploy:');
  console.log('   npm run build');
  console.log('   firebase deploy');
}