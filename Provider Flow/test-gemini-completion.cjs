// Teste específico de chat completion com Gemini
require('dotenv').config({ path: '../.env' });

const https = require('https');

const config = {
    flowBaseUrl: process.env.FLOW_BASE_URL || "https://flow.ciandt.com",
    flowTenant: process.env.FLOW_TENANT || "cit",
    flowClientId: process.env.FLOW_CLIENT_ID,
    flowClientSecret: process.env.FLOW_CLIENT_SECRET,
    flowAppToAccess: process.env.FLOW_APP_TO_ACCESS || "llm-api",
    flowAgent: "chat"
};

// Função para fazer requisição HTTP
function makeRequest(url, options, data) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, options, (res) => {
            let body = '';
            res.on('data', (chunk) => body += chunk);
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(body);
                    resolve({ status: res.statusCode, data: parsed, headers: res.headers });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body, headers: res.headers });
                }
            });
        });

        req.on('error', reject);

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function testAuthentication() {
    const authUrl = `${config.flowBaseUrl}/auth-engine-api/v1/api-key/token`;

    const authPayload = {
        clientId: config.flowClientId,
        clientSecret: config.flowClientSecret,
        appToAccess: config.flowAppToAccess
    };

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flowTenant
        }
    };

    const response = await makeRequest(authUrl, options, authPayload);
    return response.data.access_token;
}

async function testGeminiCompletion(token) {
    console.log("\n🤖 Testando Gemini 2.5 Pro...");

    const chatUrl = `${config.flowBaseUrl}/ai-orchestration-api/v1/google/generateContent`;

    // Payload específico para Gemini conforme documentação
    const payload = {
        "model": "gemini-2.5-pro",
        "contents": [
            {
                "parts": [
                    {
                        "text": "You are a helpful assistant."
                    }
                ],
                "role": "user"
            },
            {
                "parts": [
                    {
                        "text": "What is the capital of Brazil?"
                    }
                ],
                "role": "user"
            }
        ],
        "generationConfig": {
            "maxOutputTokens": 100,
            "temperature": 0.7
        }
    };

    console.log("📤 Gemini Payload:", JSON.stringify(payload, null, 2));

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'FlowTenant': config.flowTenant,
            'FlowAgent': config.flowAgent
        }
    };

    try {
        const response = await makeRequest(chatUrl, options, payload);
        console.log("📊 Status:", response.status);

        if (response.status === 200) {
            console.log("✅ Gemini completion bem-sucedido!");
            console.log("💬 Resposta:");
            console.log(JSON.stringify(response.data, null, 2));
        } else {
            console.log("❌ Falha no Gemini completion");
            console.log("📋 Status:", response.status);
            console.log("📋 Resposta:", response.data);
        }
    } catch (error) {
        console.error("❌ Erro no Gemini completion:", error.message);
    }
}

async function main() {
    try {
        console.log("🔐 Autenticando...");
        const token = await testAuthentication();
        console.log("✅ Token obtido");

        await testGeminiCompletion(token);

    } catch (error) {
        console.error("❌ Erro geral:", error.message);
    }

    console.log("\n🏁 Teste Gemini concluído");
}

main();