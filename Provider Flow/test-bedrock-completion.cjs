// Teste específico de chat completion com Amazon Bedrock
require('dotenv').config({ path: '../.env' });

const https = require('https');

const config = {
    flowBaseUrl: process.env.FLOW_BASE_URL || "https://flow.ciandt.com",
    flowTenant: process.env.FLOW_TENANT || "cit",
    flowClientId: process.env.FLOW_CLIENT_ID,
    flowClientSecret: process.env.FLOW_CLIENT_SECRET,
    flowAppToAccess: process.env.FLOW_APP_TO_ACCESS || "llm-api",
    flowAgent: "chat"
};

// Função para fazer requisição HTTP
function makeRequest(url, options, data) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, options, (res) => {
            let body = '';
            res.on('data', (chunk) => body += chunk);
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(body);
                    resolve({ status: res.statusCode, data: parsed, headers: res.headers });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body, headers: res.headers });
                }
            });
        });

        req.on('error', reject);

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function testAuthentication() {
    const authUrl = `${config.flowBaseUrl}/auth-engine-api/v1/api-key/token`;

    const authPayload = {
        clientId: config.flowClientId,
        clientSecret: config.flowClientSecret,
        appToAccess: config.flowAppToAccess
    };

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flowTenant
        }
    };

    const response = await makeRequest(authUrl, options, authPayload);
    return response.data.access_token;
}

async function testBedrockCompletion(token) {
    console.log("\n⚡ Testando Claude 3.7 Sonnet...");

    const chatUrl = `${config.flowBaseUrl}/ai-orchestration-api/v1/bedrock/invoke`;

    // Payload específico para Bedrock conforme documentação
    const payload = {
        "allowedModels": ["anthropic.claude-37-sonnet"],
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 100,
        "messages": [
            {
                "content": [
                    {
                        "text": "What is the capital of Brazil?",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "system": "You are a helpful assistant."
    };

    console.log("📤 Bedrock Payload:", JSON.stringify(payload, null, 2));

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'FlowTenant': config.flowTenant,
            'FlowAgent': config.flowAgent
        }
    };

    try {
        const response = await makeRequest(chatUrl, options, payload);
        console.log("📊 Status:", response.status);

        if (response.status === 200) {
            console.log("✅ Bedrock completion bem-sucedido!");
            console.log("💬 Resposta:");
            console.log(JSON.stringify(response.data, null, 2));
        } else {
            console.log("❌ Falha no Bedrock completion");
            console.log("📋 Status:", response.status);
            console.log("📋 Resposta:", response.data);
        }
    } catch (error) {
        console.error("❌ Erro no Bedrock completion:", error.message);
    }
}

async function testNovaCompletion(token) {
    console.log("\n🆕 Testando Amazon Nova Lite...");

    const chatUrl = `${config.flowBaseUrl}/ai-orchestration-api/v1/bedrock/invoke`;

    // Payload para Nova (formato diferente)
    const payload = {
        "allowedModels": ["amazon.nova-lite"],
        "messages": [
            {
                "content": [
                    { "text": "What is the capital of Brazil?" }
                ],
                "role": "user"
            }
        ],
        "system": [
            { "text": "You are a helpful assistant." }
        ]
    };

    console.log("📤 Nova Payload:", JSON.stringify(payload, null, 2));

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'FlowTenant': config.flowTenant,
            'FlowAgent': config.flowAgent
        }
    };

    try {
        const response = await makeRequest(chatUrl, options, payload);
        console.log("📊 Status:", response.status);

        if (response.status === 200) {
            console.log("✅ Nova completion bem-sucedido!");
            console.log("💬 Resposta:");
            console.log(JSON.stringify(response.data, null, 2));
        } else {
            console.log("❌ Falha no Nova completion");
            console.log("📋 Status:", response.status);
            console.log("📋 Resposta:", response.data);
        }
    } catch (error) {
        console.error("❌ Erro no Nova completion:", error.message);
    }
}

async function main() {
    try {
        console.log("🔐 Autenticando...");
        const token = await testAuthentication();
        console.log("✅ Token obtido");

        await testBedrockCompletion(token);
        await testNovaCompletion(token);

    } catch (error) {
        console.error("❌ Erro geral:", error.message);
    }

    console.log("\n🏁 Teste Bedrock concluído");
}

main();