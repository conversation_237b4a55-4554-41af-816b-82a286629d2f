// Teste específico de chat completion com Azure Foundry (DeepSeek-R1)
require('dotenv').config({ path: '../.env' });

const https = require('https');

const config = {
    flowBaseUrl: process.env.FLOW_BASE_URL || "https://flow.ciandt.com",
    flowTenant: process.env.FLOW_TENANT || "cit",
    flowClientId: process.env.FLOW_CLIENT_ID,
    flowClientSecret: process.env.FLOW_CLIENT_SECRET,
    flowAppToAccess: process.env.FLOW_APP_TO_ACCESS || "llm-api",
    flowAgent: "chat"
};

// Função para fazer requisição HTTP
function makeRequest(url, options, data) {
    return new Promise((resolve, reject) => {
        const req = https.request(url, options, (res) => {
            let body = '';
            res.on('data', (chunk) => body += chunk);
            res.on('end', () => {
                try {
                    const parsed = JSON.parse(body);
                    resolve({ status: res.statusCode, data: parsed, headers: res.headers });
                } catch (e) {
                    resolve({ status: res.statusCode, data: body, headers: res.headers });
                }
            });
        });

        req.on('error', reject);

        if (data) {
            req.write(JSON.stringify(data));
        }
        req.end();
    });
}

async function testAuthentication() {
    const authUrl = `${config.flowBaseUrl}/auth-engine-api/v1/api-key/token`;

    const authPayload = {
        clientId: config.flowClientId,
        clientSecret: config.flowClientSecret,
        appToAccess: config.flowAppToAccess
    };

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'FlowTenant': config.flowTenant
        }
    };

    const response = await makeRequest(authUrl, options, authPayload);
    return response.data.access_token;
}

async function testFoundryCompletion(token) {
    console.log("\n🚀 Testando DeepSeek-R1...");

    const chatUrl = `${config.flowBaseUrl}/ai-orchestration-api/v1/foundry/chat/completions`;

    // Payload específico para DeepSeek-R1 conforme documentação
    const payload = {
        "model": "DeepSeek-R1",
        "messages": [
            {
                "content": "You are an AI programming assistant, utilizing the DeepSeek Coder model, developed by DeepSeek Company, and your role is to assist with questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will not answer.\n### Instruction:\nYou are a helpful assistant.\nWhat is the capital of Brazil?\n### Response:\n",
                "role": "user"
            }
        ]
    };

    console.log("📤 DeepSeek-R1 Payload:", JSON.stringify(payload, null, 2));

    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`,
            'FlowTenant': config.flowTenant,
            'FlowAgent': config.flowAgent
        }
    };

    try {
        const response = await makeRequest(chatUrl, options, payload);
        console.log("📊 Status:", response.status);

        if (response.status === 200) {
            console.log("✅ DeepSeek-R1 completion bem-sucedido!");
            console.log("💬 Resposta:");
            console.log(JSON.stringify(response.data, null, 2));
            
            // Extrair apenas o texto da resposta, removendo <think> tags se existirem
            if (response.data.choices && response.data.choices[0] && response.data.choices[0].message) {
                const content = response.data.choices[0].message.content;
                console.log("\n📝 Conteúdo limpo da resposta:");
                console.log(content.replace(/<think>[\s\S]*?<\/think>/g, '').trim());
            }
        } else {
            console.log("❌ Falha no DeepSeek-R1 completion");
            console.log("📋 Status:", response.status);
            console.log("📋 Resposta:", response.data);
        }
    } catch (error) {
        console.error("❌ Erro no DeepSeek-R1 completion:", error.message);
    }
}

async function main() {
    try {
        console.log("🔐 Autenticando...");
        const token = await testAuthentication();
        console.log("✅ Token obtido");

        await testFoundryCompletion(token);

    } catch (error) {
        console.error("❌ Erro geral:", error.message);
    }

    console.log("\n🏁 Teste DeepSeek-R1 concluído");
}

main();