# Flow Provider - Documentação Completa dos Testes

## 🎯 Visão Geral

Esta documentação é baseada **exclusivamente** nos arquivos de teste e documentação encontrados na pasta `Provider Flow/`. O Flow Provider é uma plataforma de orquestração de IA que oferece acesso unificado a múltiplos providers de LLM através de APIs REST.

### Características Identificadas nos Testes

- **Autenticação OAuth2**: Sistema de tokens JWT com expiração
- **Multi-Provider**: Azure OpenAI, Google Gemini, Amazon Bedrock, Azure Foundry
- **Migrações de Ambiente**: Sistema de variáveis centralizadas
- **Testes Abrangentes**: 13 arquivos cobrindo todos os cenários
- **Debugging Especializado**: Ferramentas específicas para cada provider

## 📁 Arquivos de Teste Analisados

### Documentação Principal (3 arquivos)

```
Provider Flow/
├── FLOW.md                           # Documentação técnica da API
├── Flow Provider Implementation Guide.md # Guia de implementação
└── README-ENV-MIGRATION.md          # Migração de variáveis de ambiente
```

### Scripts de Teste (10 arquivos)

```
Provider Flow/
├── test-flow-api.http               # Testes HTTP completos da API
├── test-flow-api.sh                 # Script principal de teste
├── test-flow-chat-api.sh            # Teste específico de chat
├── test-flow-models-api.sh          # Teste de listagem de modelos
├── test-flow-simple.js              # Teste básico de conectividade
├── test-flow-connection.js          # Teste detalhado de conexão
├── test-flow-integration.js         # Teste de integração completa
├── test-flow-roo-integration.js     # Teste integração com Roo
├── test-flow-debug.js               # Ferramentas avançadas de debug
└── test-gemini-payload.js           # Teste específico payload Gemini
```

## ⚙️ Configuração e Variáveis de Ambiente

### Configuração Padrão dos Testes

Baseado nos arquivos de teste, a configuração padrão utiliza:

```env
# Configuração básica obrigatória
FLOW_BASE_URL=https://flow.ciandt.com
FLOW_TENANT=cit
FLOW_CLIENT_ID=your-client-id
FLOW_CLIENT_SECRET=your-client-secret
FLOW_APP_TO_ACCESS=llm-api
```

### Configurações Descobertas nos Scripts

```bash
# Configurações com valores padrão encontradas
FLOW_BASE_URL="${FLOW_BASE_URL:-https://flow.ciandt.com}"
FLOW_TENANT="${FLOW_TENANT:-cit}"
FLOW_APP_TO_ACCESS="${FLOW_APP_TO_ACCESS:-llm-api}"
```

### Headers Padrão Identificados

```http
# Headers obrigatórios para todas as requisições
Content-Type: application/json
Accept: application/json
FlowTenant: {FLOW_TENANT}
Authorization: Bearer {token}
FlowAgent: test-script
```

## 🔐 Sistema de Autenticação

### Processo de Autenticação Testado

Conforme documentado nos testes, o processo de autenticação segue este padrão:

1. **Endpoint**: `POST /auth-engine-api/v1/api-key/token`
2. **Headers Obrigatórios**:
   - `Content-Type: application/json`
   - `Accept: application/json`
   - `FlowTenant: {FLOW_TENANT}`

3. **Payload de Autenticação**:
```json
{
  "clientId": "{{FLOW_CLIENT_ID}}",
  "clientSecret": "{{FLOW_CLIENT_SECRET}}",
  "appToAccess": "{{FLOW_APP_TO_ACCESS}}"
}
```

4. **Resposta Esperada**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

### Configurações Importantes de Produção

**Constantes de Configuração Recomendadas**:
```javascript
const FlowConfig = {
    TOKEN_EXPIRY_MINUTES: 55, // 55 minutos (margem de segurança dos 60 minutos)
    API_PATH: "/ai-orchestration-api/v1",
    AUTH_PATH: "/auth-engine-api/v1/api-key/token",
    DEFAULT_APP_TO_ACCESS: "llm-api",
    DEFAULT_AGENT: "chat",
    DEFAULT_BASE_URL: "https://flow.ciandt.com",
    REQUEST_TIMEOUT: 30000, // 30 segundos
};
```

**⚠️ Nota Importante**: O token JWT expira em 1 hora (3600 segundos), mas é recomendado renovar com 5 minutos de antecedência (55 minutos) para evitar falhas em requisições longas.

### Validação de Autenticação nos Scripts

```bash
# Extração do token usando grep e sed
TOKEN=$(echo $AUTH_RESPONSE | grep -o '"access_token":"[^"]*' | sed 's/"access_token":"//')

# Verificação de sucesso
if [ -z "$TOKEN" ]; then
  echo "Falha na autenticação"
  exit 1
else
  echo "Autenticação bem-sucedida!"
fi
```

## 🤖 Providers e Modelos Testados

### Azure OpenAI

**Endpoint**: `/ai-orchestration-api/v1/openai/chat/completions`

**Modelos Testados com Especificações Completas:**

**Modelos de Chat:**
- `gpt-4` - Contexto: N/A tokens, Capacidades: system-instruction, chat-conversation, streaming
- `gpt-4.1` - Contexto: 1.000.000 tokens, Capacidades: streaming, system-instruction, chat-conversation, image-recognition
- `gpt-4o` - Contexto: 128.000 tokens, Capacidades: streaming, system-instruction, chat-conversation, image-recognition
- `gpt-4o-mini` - Contexto: 128.000 tokens, Capacidades: streaming, system-instruction, chat-conversation, image-recognition
- `o3-mini` - Contexto: 200.000 tokens, Capacidades: streaming, system-instruction, chat-conversation, reasoning_effort

**Modelos de Embeddings:**
- `text-embedding-3-small` - Capacidades: embeddings, Vector size: 1536
- `text-embedding-ada-002` - Capacidades: embeddings, Vector size: 1536

**Endpoint Embeddings**: `/ai-orchestration-api/v1/openai/embeddings`

**Payload Exemplo GPT-4O**:
```json
{
  "model": "gpt-4o",
  "messages": [
    {
      "content": "You are a helpful assistant.",
      "role": "assistant"
    },
    {
      "content": "Generate 3 random colors in RGB format",
      "role": "user"
    }
  ]
}
```

**Payload Exemplo O3-Mini** (com reasoning_effort):
```json
{
  "model": "o3-mini",
  "messages": [
    {
      "content": "You are a helpful assistant.",
      "role": "user"
    },
    {
      "content": "Generate 3 random colors in RGB format",
      "role": "user"
    }
  ],
  "reasoning_effort": "medium"
}
```

### Google Gemini

**Endpoint**: `/ai-orchestration-api/v1/google/generateContent`

**Modelos Testados:**
- `gemini-2.0-flash` - Modelo rápido (1.048.576 tokens contexto)
- `gemini-2.5-pro` - Modelo avançado com capacidades multimodais (1.048.576 tokens contexto)

**Payload Padrão Gemini**:
```json
{
  "model": "gemini-2.0-flash",
  "contents": [
    {
      "parts": [
        {
          "text": "You are a helpful assistant."
        }
      ],
      "role": "assistant"
    },
    {
      "parts": [
        {
          "text": "Generate 3 random colors in RGB format"
        }
      ],
      "role": "user"
    }
  ]
}
```

**Gemini 2.5 Pro com Geração de Imagem**:
```json
{
  "model": "gemini-2.5-pro",
  "contents": [
    {
      "role": "user",
      "parts": [
        { "text": "Hi, can you create a 3d rendered image of a pig with wings..." }
      ]
    }
  ],
  "generationConfig": {
    "responseModalities": ["Text", "Image"]
  }
}
```

**Teste de Streaming** (usando Accept: text/event-stream):
```json
{
  "model": "gemini-2.5-pro",
  "contents": [...],
  "generationConfig": {
    "maxOutputTokens": 1000,
    "temperature": 0.7
  }
}
```

### Amazon Bedrock

**Endpoint**: `/ai-orchestration-api/v1/bedrock/invoke`

**⚠️ ATENÇÃO: Modelo Descontinuado**
- `anthropic.claude-3-sonnet` - **NÃO FUNCIONA** (erro 409: modelo não está mais disponível)

**Modelos Claude Ativos:**
- `anthropic.claude-37-sonnet` - Contexto: 200,000 tokens, Capacidades: chat-conversation, image-recognition, streaming, system-instruction

**Modelos Amazon Nova:**
- `amazon.nova-lite` - Contexto: 300,000 tokens, Capacidades: chat-conversation, image-recognition, streaming
- `amazon.nova-micro` - Contexto: 128,000 tokens, Capacidades: chat-conversation, streaming
- `amazon.nova-pro` - Contexto: 300,000 tokens, Capacidades: chat-conversation, image-recognition, streaming

**Outros Modelos:**
- `meta.llama3-70b-instruct` - Contexto: 8,000 tokens, Capacidades: chat-conversation, streaming, system-instruction

**Payload Claude 3.7 Sonnet**:
```json
{
  "allowedModels": ["anthropic.claude-37-sonnet"],
  "anthropic_version": "bedrock-2023-05-31",
  "max_tokens": 8192,
  "messages": [
    {
      "content": [
        {
          "text": "Generate 3 random colors in RGB format",
          "type": "text"
        }
      ],
      "role": "user"
    }
  ],
  "system": "You are a helpful assistant."
}
```

**Payload Amazon Nova** (formato diferente):
```json
{
  "allowedModels": ["amazon.nova-lite"],
  "messages": [
    {
      "content": [
        { "text": "Generate 3 random colors in RGB format" }
      ],
      "role": "user"
    }
  ],
  "system": [
    { "text": "You are a helpful assistant." }
  ]
}
```

### Azure Foundry

**Endpoint**: `/ai-orchestration-api/v1/foundry/chat/completions`

**Modelo Testado:**
- `DeepSeek-R1` - Modelo especializado em programação

**Payload DeepSeek-R1** (formato específico):
```json
{
  "model": "DeepSeek-R1",
  "messages": [
    {
      "content": "You are an AI programming assistant, utilizing the DeepSeek Coder model, developed by DeepSeek Company, and your role is to assist with questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will not answer.\n### Instruction:\nYou are a helpful assistant.\nGenerate 3 random colors in RGB format\n### Response:\n",
      "role": "user"
    }
  ]
}
```

### Listagem de Modelos por Provider

**Endpoints de Listagem Testados:**

```http
# Azure OpenAI
GET /ai-orchestration-api/v1/models/azure-openai?capabilities=system-instruction,chat-conversation

# Google Gemini  
GET /ai-orchestration-api/v1/models/google-gemini?capabilities=system-instruction,chat-conversation

# Amazon Bedrock
GET /ai-orchestration-api/v1/models/amazon-bedrock?capabilities=system-instruction,chat-conversation

# Azure Foundry
GET /ai-orchestration-api/v1/models/azure-foundry?capabilities=system-instruction,chat-conversation
```

## 🧪 Scripts de Teste Principais

### test-flow-api.sh - Script Completo

**Funcionalidades Testadas:**
- Carregamento automático de .env
- Validação de variáveis obrigatórias  
- Teste de autenticação
- Listagem de modelos por provider
- Teste de chat completion
- Saída colorida com debug opcional

**Estrutura de Execução:**
```bash
# 1. Carregamento de .env
if [ -f "../.env" ]; then
    set -a
    source ../.env
    set +a
fi

# 2. Validação de credenciais
if [ -z "$FLOW_CLIENT_ID" ] || [ -z "$FLOW_CLIENT_SECRET" ]; then
    echo "Erro: Credenciais obrigatórias não definidas"
    exit 1
fi

# 3. Teste de autenticação
AUTH_RESPONSE=$(curl -s -X POST "${FLOW_BASE_URL}/auth-engine-api/v1/api-key/token" ...)

# 4. Extração de token
TOKEN=$(echo $AUTH_RESPONSE | grep -o '"access_token":"[^"]*' | sed 's/"access_token":"//')
```

### test-flow-models-api.sh - Teste de Modelos

**Foco específico em:**
- Listagem de modelos Azure OpenAI
- Listagem de modelos Google Gemini
- Listagem de modelos Amazon Bedrock
- Listagem de modelos Azure Foundry
- Validação de capacidades (system-instruction, chat-conversation)

### test-flow-chat-api.sh - Teste de Chat

**Providers Testados:**
- Azure OpenAI (gpt-4, o3-mini)
- Google Gemini (gemini-2.5-pro)
- Amazon Bedrock (claude-3-sonnet, claude-37-sonnet)
- Azure Foundry (DeepSeek-R1)

**Função de Teste:**
```bash
test_chat() {
    local url=$1
    local payload=$2
    local description=$3
    
    local response=$(curl -s -w "\n%{http_code}" -X POST "$url" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${TOKEN}" \
        -H "FlowTenant: ${FLOW_TENANT}" \
        -H "FlowAgent: test-script" \
        -d "$payload")
        
    local status=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | sed '$d')
    
    if [[ $status == "200" ]]; then
        echo "Chat bem sucedido!"
        echo "$body" | jq -r '.'
    fi
}
```

## 🏗️ Arquitetura Completa do Flow Provider

### Visão Geral da Arquitetura

O Flow Provider é uma plataforma de orquestração de IA que implementa as seguintes características avançadas:

#### Características Principais
- **Acesso unificado** a Azure OpenAI, Google Gemini, Amazon Bedrock, Azure Foundry
- **Gerenciamento centralizado** de autenticação com renovação automática de tokens
- **Sistema de embeddings** integrado
- **Streaming de respostas** com suporte a SSE (Server-Sent Events)
- **Monitoramento e logging** através do FlowAgent
- **Cache inteligente** de modelos com TTL configurável
- **Seleção automática** de modelos com fallbacks inteligentes
- **Validação e mapeamento** automático de modelos incompatíveis

### Estrutura de Arquivos Identificada

```
src/api/providers/
├── flow.ts                  # FlowHandler - Implementação principal
├── flow/
│   ├── auth.ts             # TokenManager - Gerenciamento de autenticação
│   ├── config.ts           # Configurações, constantes e mapeamentos
│   ├── model-service.ts    # FlowModelService - Serviço de modelos
│   ├── model-utils.ts      # Utilitários para manipulação de modelos
│   ├── payload-generator.ts # Geração de payloads específicos por provider
│   ├── request-utils.ts    # Utilitários para requisições HTTP
│   ├── types.ts            # Definições de tipos TypeScript
│   └── utils.ts            # Utilitários gerais e debug

webview-ui/src/components/settings/providers/
├── Flow.tsx                # Interface principal de configuração do Flow
├── FlowModelSelector.tsx   # Seletor de modelos com cache e auto-load
└── __tests__/              # Testes dos componentes

webview-ui/src/utils/
├── flowModelCache.ts       # Sistema de cache de modelos
└── __tests__/
    └── flowModelCache.test.ts # Testes do sistema de cache

webview-ui/src/hooks/
└── useFlowModelCache.ts    # Hook para gerenciamento do cache

src/core/webview/webviewMessageHandler.ts
├── testFlowConnection      # Handler para teste de conexão
└── fetchFlowModels         # Handler para busca de modelos
```

### Sistema de Cache de Modelos

#### FlowModelCache
- **TTL Configurável**: Cache com tempo de vida de 60 minutos por padrão
- **Invalidação Inteligente**: Cache é invalidado quando a configuração muda
- **Armazenamento Flexível**: Suporte a localStorage e sessionStorage
- **Debug Utilities**: Ferramentas de debug disponíveis no console do navegador

```typescript
// Configuração do cache
const cacheConfig = {
    ttlMinutes: 60,           // TTL em minutos
    enabled: true,            // Habilitar/desabilitar cache
    storageType: 'localStorage' // 'localStorage' ou 'sessionStorage'
}
```

### FlowModelSelector - Seletor Avançado

- **Auto-load**: Carregamento automático quando credenciais estão configuradas
- **Fallback Inteligente**: Mapeamento automático de modelos incompatíveis
- **Validação**: Verificação se o modelo selecionado está disponível
- **Cache Visual**: Indicadores visuais quando usando cache
- **Retry Manual**: Botão para recarregar modelos manualmente

```typescript
// Mapeamento automático de modelos incompatíveis
const invalidAnthropicModels = [
    "claude-3-5-sonnet-20241022",
    "claude-3-5-haiku-20241022",
    "claude-3-opus-20240229"
]
```

## 🔧 Scripts JavaScript de Teste

### test-flow-simple.js - Teste Básico

**Objetivo:** Teste simples de conectividade com a API Flow

**Configuração através de Variáveis de Ambiente:**
```javascript
// Carregamento de .env
require('dotenv').config({ path: '../.env' });

// Configuração usando variáveis de ambiente
const config = {
    flowBaseUrl: process.env.FLOW_BASE_URL || "https://flow.ciandt.com",
    flowTenant: process.env.FLOW_TENANT || "cit",
    flowClientId: process.env.FLOW_CLIENT_ID,
    flowClientSecret: process.env.FLOW_CLIENT_SECRET,
    flowAppToAccess: process.env.FLOW_APP_TO_ACCESS || "llm-api",
    flowAgent: "chat",
    apiModelId: "gemini-2.5-pro"
};
```

### test-gemini-payload.js - Teste Específico Gemini

**Problema Identificado:** Campo "stream" causava erros no Gemini

**Soluções Testadas:**
1. **Payload Correto (sem stream):**
```javascript
const correctPayload = {
    contents: [
        {
            parts: [{ text: "Você é um assistente útil." }],
            role: "user"
        },
        {
            parts: [{ text: "Olá! Como você está?" }],
            role: "user"
        }
    ],
    generationConfig: {
        maxOutputTokens: 8192,
        temperature: 0
    },
    model: "gemini-2.5-pro"
};
```

2. **Teste de Streaming:**
```javascript
// Header específico para streaming
headers: {
    'Accept': 'text/event-stream',  // Header para streaming
    'Authorization': `Bearer ${token}`,
    'FlowTenant': config.flowTenant,
    'FlowAgent': config.flowAgent
}
```

## 🌊 Sistema de Streaming Avançado

### Características do Sistema de Streaming

Conforme documentado no FLOW.md, o Flow Provider implementa um sistema robusto de streaming SSE:

1. **Processamento de Chunks SSE**: Sistema de buffer para fragmentação de dados
2. **Extração de Chunks Completos**: Uso de regex para identificar chunks válidos
3. **Transformação Específica por Provider**: Cada provider tem seu próprio formato de streaming
4. **Headers Específicos**: Headers otimizados para streaming (`Cache-Control: no-cache`, `Connection: keep-alive`)
5. **TextDecoder com Stream**: Uso de `TextDecoder` com `stream: true` para chunks fragmentados

### Algoritmo de Extração de Chunks

O FlowHandler implementa um algoritmo duplo de extração:

1. **Primeiro**: Regex `/data: .*?\n\n/gs` para padrões completos
2. **Fallback**: Processamento linha-por-linha com agrupamento baseado em "data: " e linhas vazias

### Implementação por Provider

#### Azure OpenAI
```json
{
    "stream": true,
    "messages": [...]
}
```
**Formato de Streaming**: Suporte a `delta.content` e `message.content`

#### Google Gemini
```json
{
    "stream": true,
    "contents": [...]
}
```
**Formato de Streaming**: `candidates[0].content.parts[0].text`

#### Amazon Bedrock
```json
{
    "stream": true,
    "messages": [...]
}
```
**Formato de Streaming**: Múltiplos formatos (`content_block_delta`, `message_delta`, `content array`)

### Headers para Streaming

```http
Cache-Control: no-cache
Connection: keep-alive
Accept: text/event-stream
Authorization: Bearer {{token}}
FlowTenant: {{tenant}}
FlowAgent: {{agent}}
```

## ⚙️ Sistema de Payload Generation

### Características do Payload Generator

O Flow Provider implementa geração de payloads específica por provider com tratamentos especiais:

1. **Azure OpenAI**: Merge de system messages para modelos O1/O3
2. **Google Gemini**: Conversão role assistant→model
3. **Amazon Bedrock**: anthropic_version condicional para modelos Nova
4. **Azure Foundry**: Formatação DeepSeek-R1 com marcadores de instrução

### Tratamento Especial de Modelos O1/O3

Os modelos O1 e O3 requerem tratamento especial:

```typescript
// Detecção de modelos O1/O3
const isO1OrO3Model = (modelId: string): boolean => {
    return modelId.includes('o1-') || modelId.includes('o3-')
}

// Tratamento especial (sem temperature)
if (isO1OrO3Model(modelId)) {
    // Remove temperature para modelos O1/O3
    delete payload.temperature

    // Merge system messages
    payload.messages = mergeSystemMessages(payload.messages)
}
```

### Formatação por Provider

#### Azure OpenAI - Merge de System Messages
```typescript
const mergeSystemMessages = (messages: Message[]): Message[] => {
    const systemMessages = messages.filter(m => m.role === 'system')
    const otherMessages = messages.filter(m => m.role !== 'system')

    if (systemMessages.length > 1) {
        const mergedContent = systemMessages.map(m => m.content).join('\n\n')
        return [
            { role: 'system', content: mergedContent },
            ...otherMessages
        ]
    }
    return messages
}
```

#### Google Gemini - Conversão de Roles
```typescript
const convertRoleForGemini = (role: string): string => {
    return role === 'assistant' ? 'model' : role
}
```

#### Amazon Bedrock - Anthropic Version Condicional
```typescript
const getAnthropicVersion = (modelId: string): string => {
    // Para modelos Nova, usar versão específica
    if (modelId.includes('nova-')) {
        return 'bedrock-2023-05-31'
    }
    return 'bedrock-2023-05-31'
}
```

### test-flow-integration.js - Teste de Integração

**Fluxo Completo Testado:**
1. Autenticação → obter token
2. Listagem de modelos por provider
3. Chat completion com diferentes providers
4. Validação de respostas

## 📋 Teste HTTP com REST Client

### test-flow-api.http - Arquivo Completo

**Estrutura de Variáveis:**
```http
### Configurações Globais
@baseUrl = {{$dotenv FLOW_BASE_URL}}
@flowTenant = {{$dotenv FLOW_TENANT}}
@clientId = {{$dotenv FLOW_CLIENT_ID}}
@clientSecret = {{$dotenv FLOW_CLIENT_SECRET}}
@appToAccess = {{$dotenv FLOW_APP_TO_ACCESS}}
@agent = test-script
```

**Processo de Autenticação:**
```http
### 1. Gerar token de autenticação
# @name auth
POST {{baseUrl}}/auth-engine-api/v1/api-key/token
Content-Type: application/json
Accept: application/json
FlowTenant: {{flowTenant}}

{
    "clientId": "{{clientId}}",
    "clientSecret": "{{clientSecret}}",
    "appToAccess": "{{appToAccess}}"
}

### Definir token para as próximas requisições
@token = {{auth.response.body.access_token}}
```

**Testes de Modelos por Provider:**
```http
### Azure OpenAI
GET {{baseUrl}}/ai-orchestration-api/v1/models/azure-openai?capabilities=system-instruction,chat-conversation
Authorization: Bearer {{token}}
FlowTenant: {{flowTenant}}

### Google Gemini
GET {{baseUrl}}/ai-orchestration-api/v1/models/google-gemini?capabilities=system-instruction,chat-conversation
Authorization: Bearer {{token}}
FlowTenant: {{flowTenant}}

### Amazon Bedrock
GET {{baseUrl}}/ai-orchestration-api/v1/models/amazon-bedrock?capabilities=system-instruction,chat-conversation
Authorization: Bearer {{token}}
FlowTenant: {{flowTenant}}

### Azure Foundry
GET {{baseUrl}}/ai-orchestration-api/v1/models/azure-foundry?capabilities=system-instruction,chat-conversation
Authorization: Bearer {{token}}
FlowTenant: {{flowTenant}}
```

## 🛠️ Debugging e Troubleshooting

### Logs Estruturados nos Scripts

**Debug Habilitado com Variável de Ambiente:**
```bash
# Habilitar debug
DEBUG=true ./test-flow-api.sh

# Função de debug no script
debug() {
    if [[ "$DEBUG" == "true" ]]; then
        echo -e "${YELLOW}[DEBUG] $1${NC}"
    fi
}
```

**Saída Colorizada:**
```bash
# Cores definidas nos scripts
GREEN='\\033[0;32m'   # Sucesso
RED='\\033[0;31m'     # Erro
BLUE='\\033[0;34m'    # Informação
YELLOW='\\033[1;33m'  # Debug/Aviso
NC='\\033[0m'         # Reset
```

### Problemas Comuns Identificados nos Testes

| Problema | Arquivo de Teste | Solução Documentada |
|----------|------------------|---------------------|
| Campo "stream" causa erro no Gemini | `test-gemini-payload.js` | Remover campo stream do payload |
| Token inválido/expirado | `test-flow-simple.js` | Verificar FLOW_CLIENT_ID e FLOW_CLIENT_SECRET |
| Modelo não disponível | `test-flow-models-api.sh` | Listar modelos disponíveis primeiro |
| Erro CORS em desenvolvimento | Scripts shell | Usar curl ao invés de fetch para testes |
| Payload incorreto | `test-flow-api.http` | Verificar formato específico do provider |

### Comandos de Debug dos Scripts

```bash
# Teste básico de conectividade
cd "Provider Flow"
node test-flow-simple.js

# Teste completo com debug
DEBUG=true ./test-flow-api.sh

# Teste apenas modelos
./test-flow-models-api.sh

# Teste específico de chat
./test-flow-chat-api.sh

# Debug específico do Gemini
node test-gemini-payload.js

# Ferramentas avançadas de debug
node test-flow-debug.js
```

### Validação de Configuração

**Verificação de Variáveis Obrigatórias:**
```bash
# Verificação nos scripts shell
if [ -z "$FLOW_CLIENT_ID" ] || [ -z "$FLOW_CLIENT_SECRET" ]; then
    echo "Erro: FLOW_CLIENT_ID e FLOW_CLIENT_SECRET devem estar definidos no arquivo .env"
    exit 1
fi
```

**Verificação em JavaScript:**
```javascript
// Verificação nos scripts JS
if (!process.env.FLOW_CLIENT_ID || !process.env.FLOW_CLIENT_SECRET) {
    console.error("Erro: FLOW_CLIENT_ID e FLOW_CLIENT_SECRET devem estar definidos no arquivo .env");
    process.exit(1);
}
```

## 📊 Sistema de Embeddings

### Modelos de Embeddings Disponíveis

Conforme documentado no FLOW.md, o Flow Provider suporta embeddings através do Azure OpenAI:

**Endpoint**: `/ai-orchestration-api/v1/openai/embeddings`

**Modelos Suportados:**
- `text-embedding-ada-002` - Modelo clássico de embeddings
- `text-embedding-3-small` - Modelo otimizado e mais eficiente

**Payload de Exemplo:**
```json
{
  "input": "Text to convert to vector",
  "user": "flow",
  "allowedModels": ["text-embedding-3-small"]
}
```

**Headers Obrigatórios:**
```http
Content-Type: application/json
Accept: application/json
Authorization: Bearer {{token}}
FlowTenant: {{tenant}}
FlowAgent: {{agent}}
x-ms-model-mesh-model-name: text-embedding-3-small
```

**Nota Importante**: O header `x-ms-model-mesh-model-name` é necessário para o correto roteamento do modelo de embedding e deve corresponder ao primeiro modelo listado em `allowedModels`.

## 📈 Comparação entre Modelos

### Modelos de Chat por Casos de Uso

| Modelo | Provider | Casos de Uso Ideais | Contexto | Capacidades |
|--------|----------|---------------------|----------|-------------|
| **Azure OpenAI** |
| GPT-4O | Azure OpenAI | Tarefas complexas, análise profunda | 128,000 tokens | streaming, system-instruction, chat-conversation, image-recognition |
| GPT-4O Mini | Azure OpenAI | Tarefas médias a complexas | 128,000 tokens | streaming, system-instruction, chat-conversation, image-recognition |
| O3-Mini | Azure OpenAI | Raciocínio avançado, tarefas complexas | 200,000 tokens | streaming, system-instruction, chat-conversation |
| GPT-4 | Azure OpenAI | Uso geral, compatibilidade | - | system-instruction, chat-conversation, streaming |
| **Google Gemini** |
| Gemini 2.0 Flash | Google | Respostas ultra-rápidas | 8,192 tokens | streaming, chat-conversation, image-recognition, system-instruction |
| Gemini 2.5 Pro | Google | Contextos longos, multimodal | 1,048,576 tokens | streaming, chat-conversation, image-recognition, system-instruction |
| **Amazon Bedrock** |
| Claude 3 Sonnet | Amazon Bedrock | Análise detalhada, contextos longos | 200,000 tokens | chat-conversation, image-recognition, streaming |
| Claude 3.7 Sonnet | Amazon Bedrock | Análise avançada, alta precisão | 200,000 tokens | chat-conversation, image-recognition, streaming |
| Nova Lite | Amazon Bedrock | Tarefas gerais, boa velocidade | 300,000 tokens | chat-conversation, image-recognition, streaming |
| Nova Micro | Amazon Bedrock | Tarefas simples, rápidas | 128,000 tokens | chat-conversation, streaming |
| Nova Pro | Amazon Bedrock | Tarefas avançadas | 300,000 tokens | chat-conversation, image-recognition, streaming |
| Llama3 70B | Amazon Bedrock | Raciocínio complexo | 200,000 tokens | chat-conversation, image-recognition, streaming |
| **Azure Foundry** |
| DeepSeek-R1 | Azure Foundry | Tarefas técnicas e programação | - | chat-conversation |

### Modelos de Embeddings

| Modelo | Tamanho do Vetor | Casos de Uso Ideais | Performance |
|--------|------------------|---------------------|-------------|
| text-embedding-ada-002 | 1536 | Busca semântica, classificação | Padrão |
| text-embedding-3-small | 1536 | Aplicações otimizadas | Melhor |

## 🔧 Ferramentas de Debug e Desenvolvimento

### Debug Utilities

O Flow Provider inclui várias ferramentas para debug e desenvolvimento:

#### Console Debug Tools
```javascript
// Ferramentas disponíveis no console do navegador
window.FlowModelCacheDebug = {
    info: () => console.table(FlowModelCacheDebug.getCacheInfo()),
    clear: () => FlowModelCacheDebug.clearCache(),
    disable: () => FlowModelCacheDebug.updateConfig({ enabled: false }),
    enable: () => FlowModelCacheDebug.updateConfig({ enabled: true }),
    setTTL: (minutes) => FlowModelCacheDebug.updateConfig({ ttlMinutes: minutes })
}
```

#### Logging Detalhado
```typescript
// Habilitar debug logging
const debug = (message: string, data?: any) => {
    if (process.env.DEBUG === 'true') {
        console.log(`[Flow] ${message}`, data || '')
    }
}

// Logs estruturados para cada operação
console.log("🚀 [FlowHandler] createMessage iniciado", {
    systemPromptLength: systemPrompt.length,
    messagesCount: messages.length,
    metadata
})
```

### Sistema de Request Utils com Retry Avançado

O Flow implementa utilitários avançados de requisição:

```typescript
// Retry automático com exponential backoff
const retryWithBackoff = async (fn: () => Promise<any>, maxRetries = 3) => {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await fn()
        } catch (error) {
            if (i === maxRetries - 1) throw error

            // Exponential backoff com jitter
            const delay = Math.pow(2, i) * 1000 + Math.random() * 1000
            await new Promise(resolve => setTimeout(resolve, delay))
        }
    }
}

// Timeout configurável
const requestWithTimeout = (url: string, options: any, timeout: number) => {
    return Promise.race([
        fetch(url, options),
        new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), timeout)
        )
    ])
}
```

## 📚 Migração de Variáveis de Ambiente

### README-ENV-MIGRATION.md

Conforme documentado no arquivo de migração, o projeto evoluiu de credenciais hardcoded para variáveis de ambiente:

#### Benefícios da Refatoração

1. **Segurança**: Credenciais sensíveis não estão mais hardcoded no código
2. **Flexibilidade**: Configurações podem ser alteradas sem modificar código
3. **Manutenibilidade**: Configurações centralizadas em um único local
4. **Compatibilidade**: Mantida funcionalidade existente com fallbacks
5. **Ambiente**: Suporte a diferentes ambientes (dev, test, prod)

#### Variáveis de Ambiente Obrigatórias

```bash
# Flow Provider Configuration (Required for Flow provider)
FLOW_BASE_URL="https://flow.ciandt.com"                                # Flow API base URL
FLOW_TENANT="cit"                                                      # Flow tenant identifier
FLOW_CLIENT_ID="306e7927-d8cd-4d19-910d-e9005867b67d"                 # Flow client ID for authentication
FLOW_CLIENT_SECRET="your-flow-client-secret-here"                     # Flow client secret for authentication
FLOW_APP_TO_ACCESS="llm-api"                                           # Flow application to access
```

#### Arquivos Modificados na Migração

1. **Shell Scripts de Teste**: `test-flow-api.sh`, `test-flow-models-api.sh`, `test-flow-chat-api.sh`
2. **Scripts JavaScript**: `test-flow-simple.js`, `test-flow-connection.js`, `test-flow-integration.js`, etc.
3. **Arquivo HTTP**: `test-flow-api.http`
4. **Configuração TypeScript**: `src/api/providers/flow/config.ts`

**Antes (hardcoded):**
```javascript
const config = {
    flowBaseUrl: "https://flow.ciandt.com",
    flowTenant: "cit",
    flowClientId: "hardcoded-client-id",
    flowClientSecret: "hardcoded-secret"
};
```

**Depois (configurável):**
```javascript
const config = {
    flowBaseUrl: process.env.FLOW_BASE_URL || "https://flow.ciandt.com",
    flowTenant: process.env.FLOW_TENANT || "cit",
    flowClientId: process.env.FLOW_CLIENT_ID,
    flowClientSecret: process.env.FLOW_CLIENT_SECRET,
    flowAppToAccess: process.env.FLOW_APP_TO_ACCESS || "llm-api"
};
```

## 🎯 Casos de Uso dos Testes

### Teste de Conectividade Básica
```bash
# Para verificar se as credenciais estão funcionando
node test-flow-simple.js
```

### Teste de Modelos Disponíveis
```bash
# Para descobrir quais modelos cada provider oferece
./test-flow-models-api.sh
```

### Teste de Chat Completo
```bash
# Para validar funcionamento de chat com todos os providers
./test-flow-chat-api.sh
```

### Teste de Payload Específico
```bash
# Para debugar problemas específicos do Gemini
node test-gemini-payload.js
```

### Teste via REST Client
```http
# Para teste interativo no VS Code
# Usar arquivo test-flow-api.http
```

## 📚 Resumo dos Endpoints Testados

### Endpoints de Autenticação
```http
POST /auth-engine-api/v1/api-key/token
```

### Endpoints de Listagem de Modelos
```http
GET /ai-orchestration-api/v1/models/azure-openai?capabilities=system-instruction,chat-conversation
GET /ai-orchestration-api/v1/models/google-gemini?capabilities=system-instruction,chat-conversation  
GET /ai-orchestration-api/v1/models/amazon-bedrock?capabilities=system-instruction,chat-conversation
GET /ai-orchestration-api/v1/models/azure-foundry?capabilities=system-instruction,chat-conversation
```

### Endpoints de Chat Completion
```http
POST /ai-orchestration-api/v1/openai/chat/completions        # Azure OpenAI
POST /ai-orchestration-api/v1/google/generateContent         # Google Gemini
POST /ai-orchestration-api/v1/bedrock/invoke                 # Amazon Bedrock
POST /ai-orchestration-api/v1/foundry/chat/completions       # Azure Foundry
```

### Endpoints de Embeddings
```http
POST /ai-orchestration-api/v1/openai/embeddings              # Azure OpenAI Embeddings
```

---

## 📄 Conclusão

Esta documentação foi criada **exclusivamente** com base nos 3 arquivos `.md` e 10 scripts de teste encontrados na pasta `Provider Flow/`. O conjunto completo de 13 arquivos fornece uma visão abrangente e técnica do Flow Provider como plataforma de orquestração de IA.

### Principais Descobertas da Documentação Técnica:

#### Arquitetura Avançada
1. **Plataforma de Orquestração Completa** com camada de abstração entre aplicações e LLMs
2. **Sistema de Cache Inteligente** com TTL de 60 minutos e invalidação por configuração
3. **TokenManager Automático** com renovação preventiva de 1 minuto antes do vencimento
4. **FlowModelService** com busca multi-provider e modelos hardcoded como fallback
5. **Sistema de Streaming SSE Avançado** com algoritmo duplo de extração de chunks

#### Características Técnicas Avançadas
6. **Payload Generation Específico** com tratamento especial para modelos O1/O3 (sem temperature)
7. **Sistema de Request Utils** com exponential backoff, jitter e timeout configurável
8. **Mapeamento Automático** de modelos Anthropic incompatíveis para equivalentes Flow
9. **FlowModelSelector** com auto-load, cache visual e retry manual
10. **Debug Utilities** com ferramentas de console e logging estruturado

#### Providers e Modelos Completos
11. **4 Providers Ativos**: Azure OpenAI (6 modelos), Google Gemini (2 modelos), Amazon Bedrock (6 modelos), Azure Foundry (1 modelo)
12. **Sistema de Embeddings** com 2 modelos Azure OpenAI e headers específicos
13. **Capacidades Detalhadas** para cada modelo (streaming, image-recognition, system-instruction, chat-conversation)

#### Implementação e Migração
14. **Migração Completa** de hardcoded para variáveis de ambiente em todos os 10 scripts
15. **Estrutura de Arquivos Completa** com 16 arquivos TypeScript/React organizados
16. **Backend Integration** com handlers de teste de conexão e busca de modelos

### Arquivos Essenciais Identificados:

#### Documentação Principal
- `FLOW.md` - Documentação técnica completa com 1346 linhas
- `Flow Provider Implementation Guide.md` - Guia de implementação com 1524 linhas
- `README-ENV-MIGRATION.md` - Guia de migração com detalhes de refatoração

#### Scripts Críticos para Desenvolvedores
- `test-flow-api.http` - 549 linhas de testes HTTP completos
- `test-flow-api.sh` - Script principal de teste automatizado
- `test-gemini-payload.js` - Debug específico para problemas de streaming Gemini

### Funcionalidades Avançadas Documentadas:

1. **Sistema de Streaming com SSE** - Processamento de chunks fragmentados com TextDecoder
2. **Cache de Modelos com TTL** - localStorage/sessionStorage com invalidação inteligente
3. **Payload Generation Inteligente** - Formatação específica por provider com tratamentos especiais
4. **Sistema de Retry Avançado** - Exponential backoff com jitter para evitar thundering herd
5. **Debug Tools Profissionais** - Console utilities e logging estruturado para desenvolvimento

**Versão da Documentação**: 4.0 (Baseada exclusivamente nos arquivos .md)  
**Fonte**: 3 arquivos `.md` + 10 scripts de teste da pasta `Provider Flow/`  
**Última Atualização**: Janeiro 2025  
**Status**: ✅ Completamente Documentado e Arquitetado