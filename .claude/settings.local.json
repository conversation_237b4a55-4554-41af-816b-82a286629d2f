{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "mcp__ide__getDiagnostics", "Bash(ls:*)", "Bash(npm run lint)", "Bash(npm test)", "Bash(npm uninstall:*)", "Bash(npm install:*)", "Bash(node:*)", "Bash(npm test:*)", "<PERSON><PERSON>(timeout:*)", "Bash(npm run validate:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(export FLOW_CLIENT_ID=\"a994baf4-61dc-434b-a10e-59385fa282cf\")", "Bash(export FLOW_CLIENT_SECRET=\"****************************************\")", "Bash(export FLOW_APP_TO_ACCESS=\"llm-api\")", "Bash(export FLOW_TENANT=\"cit\")", "Bash(export FLOW_BASE_URL=\"https://flow.ciandt.com\")", "Bash(./test-flow-api.sh:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)"], "deny": []}}