name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint-test:
    name: <PERSON><PERSON> e Testes
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout do código
        uses: actions/checkout@v3
        
      - name: Configuração do Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          
      - name: Instalar dependências
        run: npm ci
        
      - name: Lint
        run: npm run lint
        
      - name: Testes
        run: npm test
        
      - name: Upload dos resultados de testes
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: coverage/
          
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: lint-test
    
    steps:
      - name: Checkout do código
        uses: actions/checkout@v3
        
      - name: Configuração do Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          
      - name: Instalar dependências
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Upload do artefato de build
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: dist/
          
  deploy-preview:
    name: Deploy de Preview (PR)
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: preview
      
    steps:
      - name: Checkout do código
        uses: actions/checkout@v3
        
      - name: Download do artefato de build
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: dist
          
      # Aqui seria a etapa de deploy para ambiente de preview
      # Exemplo usando Firebase (precisaria de configuração adicional)
      - name: Deploy para preview
        run: echo "Deploy de preview seria executado aqui"
        
  deploy-production:
    name: Deploy para Produção
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    needs: build
    environment:
      name: production
      url: https://seu-app.dominio.com
      
    steps:
      - name: Checkout do código
        uses: actions/checkout@v3
        
      - name: Download do artefato de build
        uses: actions/download-artifact@v3
        with:
          name: build-files
          path: dist
          
      # Aqui seria a etapa de deploy para produção
      # Exemplo usando Firebase (precisaria de configuração adicional)
      - name: Deploy para produção
        run: echo "Deploy de produção seria executado aqui"
