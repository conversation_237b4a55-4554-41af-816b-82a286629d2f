# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint with max 0 warnings
- `npm run format` - Format code with Prettier
- `npm test` - Run tests with Vitest
- `npm run test:watch` - Run tests in watch mode

### Linting and Testing
Always run `npm run lint` and `npm test` after making changes to ensure code quality.

## Architecture Overview

This is a React + TypeScript adaptive learning platform that integrates with Firebase and the Flow AI orchestration API. The application follows a layered architecture with clear separation of concerns.

### Key Architectural Layers

1. **Presentation Layer** (`/src/components`, `/src/features/*/pages`)
   - React functional components with hooks
   - Feature-based organization in `/src/features`
   - Reusable UI components in `/src/components/ui`

2. **Application Layer** (`/src/hooks`, `/src/app/providers`)
   - Custom React hooks for business logic
   - Context providers for global state management
   - React Router configuration in `/src/app/router.tsx`

3. **Domain Layer** (`/src/types`, `/src/lib`)
   - TypeScript type definitions
   - Business logic utilities
   - Logging system via `logger.ts`

4. **Infrastructure Layer** (`/src/services`)
   - Firebase integration (Auth, Firestore)
   - Flow API service for AI interactions
   - External API adapters

### State Management
- Local state: `useState` and `useReducer` for component state
- Global state: React Context API with providers (`AuthProvider`, `FlowProvider`)
- Persistence: Firestore for user data, localStorage for preferences

## Technology Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite with React plugin
- **Authentication**: Firebase Authentication
- **Database**: Firestore
- **Testing**: Vitest + React Testing Library + jsdom
- **Styling**: CSS (no specific framework detected)
- **AI Integration**: Flow API orchestration platform

## Flow API Integration

The application integrates with Flow, an AI orchestration platform that provides unified access to multiple LLM providers:

### Key Flow Components
- **FlowProvider** (`/src/components/FlowProvider.tsx`) - React context for Flow state
- **FlowService** (`/src/services/flowService.ts`) - Core API integration
- **FlowStorageService** (`/src/services/flowStorageService.ts`) - Persistence layer

### Flow Configuration
Required environment variables for Flow API:
- `VITE_FLOW_BASE_URL` - Flow API base URL
- `VITE_FLOW_TENANT` - Tenant identifier
- `VITE_FLOW_CLIENT_ID` - Client ID for authentication
- `VITE_FLOW_CLIENT_SECRET` - Client secret
- `VITE_FLOW_APP_TO_ACCESS` - Application identifier
- `VITE_FLOW_AGENT` - Agent identifier
- `VITE_FLOW_AUTH_BASE_URL` - Authentication service URL

## Project Structure Conventions

### File Organization
- Components use PascalCase naming
- Services use camelCase with Service suffix
- Types are defined in `/src/types` with descriptive names
- Custom hooks start with `use` prefix

### Import Patterns
- Path alias `@/*` maps to `src/*`
- Imports ordered: external modules, internal modules, relative imports

### Component Patterns
- Functional components with TypeScript
- Props interfaces defined inline or in separate types files
- Error handling via ErrorBoundary component

## Environment Configuration

### Required Firebase Variables
```env
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
VITE_FIREBASE_APP_ID=your-app-id
```

### Optional Variables
- `VITE_GA_ID` - Google Analytics tracking ID
- `VITE_LOG_ENDPOINT` - Remote logging endpoint
- `GEMINI_API_KEY` - Direct Gemini API access (defined in vite.config.ts)

## Code Quality Standards

- TypeScript strict mode enabled
- ESLint with max 0 warnings
- Prettier for code formatting
- Vitest for testing with jsdom environment
- No unused locals or parameters allowed