# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Build outputs
dist/
build/
dist-ssr/
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.staging

# Firebase
.firebase/
firebase-debug.log
firebase-debug.*.log
.firebaserc.local
firebase-adminsdk-*.json
service-account-*.json

# IDE and Editor files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# TypeScript
*.tsbuildinfo

# Testing
/coverage

# Misc
*.tar.gz

# Flow Provider specific (sensitive data)
flow-config.json
*.flow.json

# Backup files
*.backup
*.bak

# Lock files (uncomment based on your package manager preference)
# package-lock.json  # Uncomment if using yarn exclusively
# yarn.lock          # Uncomment if using npm exclusively

# Documentation build
docs/build/

# Sentry
.sentryclirc

# Vercel
.vercel

# Turbo
.turbo

# Local Netlify folder
.netlify
