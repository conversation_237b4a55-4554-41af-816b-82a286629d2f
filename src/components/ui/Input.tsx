import React, { forwardRef } from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isFullWidth?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  helperText,
  error,
  leftIcon,
  rightIcon,
  isFullWidth = true,
  className = '',
  id,
  ...props
}, ref) => {
  // Gera um ID aleatório se não for fornecido
  const inputId = id || `input-${Math.random().toString(36).substring(2, 11)}`;
  
  // Classes base para o input usando sistema de temas
  const baseInputClasses = 'input-theme block rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none';
  
  // Classes adicionais baseadas em props
  const inputStateClasses = error 
    ? 'error-state' 
    : '';
  
  // Classes de largura
  const widthClasses = isFullWidth ? 'w-full' : '';
  
  // Classes dinâmicas combinadas
  const inputClasses = `
    ${baseInputClasses}
    ${inputStateClasses}
    ${widthClasses}
    ${leftIcon ? 'pl-10' : ''}
    ${rightIcon ? 'pr-10' : ''}
    ${className}
  `;
  
  return (
    <div className={`${isFullWidth ? 'w-full' : ''} mb-4`}>
      {label && (
        <label 
          htmlFor={inputId}
          className="block text-sm font-medium text-theme-primary mb-1"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-theme-muted">
            {leftIcon}
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={inputClasses}
          aria-invalid={!!error}
          aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper-text` : undefined}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-theme-muted">
            {rightIcon}
          </div>
        )}
      </div>
      
      {error ? (
        <p id={`${inputId}-error`} className="mt-1 text-sm text-red-600">
          {error}
        </p>
      ) : helperText ? (
        <p id={`${inputId}-helper-text`} className="mt-1 text-sm text-theme-secondary">
          {helperText}
        </p>
      ) : null}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
