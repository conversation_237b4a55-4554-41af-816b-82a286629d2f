import React from 'react';
import { CognitiveType } from '@/types';
import { Button, ButtonProps } from './Button';

interface AdaptiveButtonProps extends Omit<ButtonProps, 'variant'> {
  cognitiveType: CognitiveType;
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
}

export const AdaptiveButton: React.FC<AdaptiveButtonProps> = ({
  cognitiveType,
  variant = 'primary',
  className = '',
  size = 'md',
  children,
  ...props
}) => {
  // Usa o sistema de temas unificado - as cores são aplicadas automaticamente via CSS variables
  const profileClasses = {
    [CognitiveType.ADHD]: {
      primary: 'btn-primary',
      secondary: 'btn-accent',
    },
    [CognitiveType.AUTISM]: {
      primary: 'btn-primary',
      secondary: 'btn-accent',
    },
    [CognitiveType.DYSLEXIA]: {
      primary: 'btn-primary',
      secondary: 'btn-accent',
    },
    [CognitiveType.DEFAULT]: {
      primary: 'btn-primary',
      secondary: 'btn-secondary',
    },
  };

  // const currentProfile = profileClasses[cognitiveType] || profileClasses[CognitiveType.DEFAULT];

  // Classes específicas para adaptações cognitivas
  let adaptiveClasses = '';

  if (cognitiveType === CognitiveType.DYSLEXIA) {
    adaptiveClasses += ' tracking-wide'; // Espaçamento de letras
    if (size !== 'sm') adaptiveClasses += ' min-h-[48px]'; // Altura mínima maior
  } else if (cognitiveType === CognitiveType.ADHD) {
    if (size !== 'sm') adaptiveClasses += ' min-h-[44px]';
  } else if (cognitiveType === CognitiveType.AUTISM) {
    adaptiveClasses += ' border-2 border-transparent focus:border-theme-focus'; // Foco mais claro
    if (size !== 'sm') adaptiveClasses += ' min-h-[44px]';
  }

  // Determina a variante do botão baseada no perfil cognitivo
  let buttonVariant: 'primary' | 'secondary' | 'outline' | 'accent' = 'primary';

  if (variant === 'primary') {
    buttonVariant = 'primary';
  } else if (variant === 'secondary') {
    buttonVariant = cognitiveType === CognitiveType.DEFAULT ? 'secondary' : 'accent';
  } else if (variant === 'outline') {
    buttonVariant = 'outline';
  } else {
    buttonVariant = 'primary';
  }

  // Classes finais combinadas
  const finalClassName = `${adaptiveClasses} ${className}`;

  return (
    <Button
      variant={buttonVariant}
      size={size}
      className={finalClassName}
      {...props}
    >
      {children}
    </Button>
  );
};