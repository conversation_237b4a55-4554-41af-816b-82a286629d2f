import React from 'react';
import { ChatMessage } from '@/types/chat';
import { Button } from '@/components/ui/Button';
import { AuthUser } from '@/types/auth';

interface ChatBubbleProps {
  message: ChatMessage;
  isEditing: boolean;
  editingText: string;
  onStartEdit: (id: string, text: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onDelete: (id: string) => void;
  onEditTextChange: (text: string) => void;
  renderMessageContent: (text: string) => React.ReactNode;
  user?: AuthUser | null; // Dados do usuário para exibir avatar
}

export const ChatBubble: React.FC<ChatBubbleProps> = ({
  message,
  isEditing,
  editingText,
  onStartEdit,
  onSaveEdit,
  onCancelEdit,
  onDelete,
  onEditTextChange,
  renderMessageContent,
  user,
}) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4 group`}>
      {/* Layout similar para ambos: Avatar + Conteúdo */}
      <div className={`flex items-start space-x-3 max-w-[85%] sm:max-w-[75%] ${
        isUser ? 'flex-row-reverse space-x-reverse' : 'flex-row'
      }`}>
        
        {/* Avatar - sempre visível e maior */}
        <div className="flex-shrink-0 mt-1">
          {isUser ? (
            /* Avatar do usuário */
            <div className="w-8 h-8 rounded-full border-2 border-theme-accent overflow-hidden shadow-sm">
              {user?.photoURL ? (
                <img
                  src={user.photoURL}
                  alt={user.displayName ?? 'Usuário'}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-theme-accent flex items-center justify-center">
                  <span className="text-white text-sm font-bold">
                    {user?.displayName?.charAt(0) ?? user?.email?.charAt(0) ?? 'U'}
                  </span>
                </div>
              )}
            </div>
          ) : (
            /* Avatar do assistente */
            <div className="w-8 h-8 rounded-full bg-theme-secondary border-2 border-theme-primary flex items-center justify-center shadow-sm">
              <span className="text-lg">🤖</span>
            </div>
          )}
        </div>

        {/* Conteúdo da mensagem */}
        <div className="flex-1 min-w-0">
          {/* Nome do remetente */}
          <div className={`text-xs font-medium text-theme-muted mb-1 ${
            isUser ? 'text-right' : 'text-left'
          }`}>
            {isUser ? (user?.displayName ?? 'Você') : 'FlowEd'}
          </div>

          {/* Bolha da mensagem - Design unificado */}
          <div className={`chat-bubble rounded-lg px-4 py-3 shadow-sm relative border border-theme ${
            isUser
              ? 'bg-theme-secondary text-theme-primary rounded-tr-sm'
              : 'bg-theme-secondary text-theme-primary rounded-tl-sm'
          }`}>

            {/* Conteúdo da mensagem */}
            {isEditing ? (
              <div className="space-y-2">
                <textarea
                  value={editingText}
                  onChange={(e) => onEditTextChange(e.target.value)}
                  className="w-full bg-theme-primary text-theme-primary border border-theme rounded p-2 resize-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus text-sm"
                  rows={3}
                  autoFocus
                />
                <div className="flex space-x-2">
                  <Button size="sm" variant="success" onClick={onSaveEdit}>
                    Salvar
                  </Button>
                  <Button size="sm" variant="outline" onClick={onCancelEdit}>
                    Cancelar
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-sm leading-relaxed whitespace-pre-line">
                {renderMessageContent(message.text)}
              </div>
            )}

          </div>

          {/* Footer com Timestamp e Botões de Ação */}
          {!isEditing && (
            <div className="flex items-center justify-between mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
              {/* Timestamp sempre à esquerda */}
              <div className="text-xs text-theme-muted opacity-70 text-[10px]">
                {(() => {
                  const date = new Date(message.timestamp);
                  // Verificar se a data é válida
                  if (isNaN(date.getTime())) {
                    const now = new Date();
                    return `${now.toLocaleDateString('pt-BR')} ${now.toLocaleTimeString('pt-BR', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}`;
                  }
                  return `${date.toLocaleDateString('pt-BR')} ${date.toLocaleTimeString('pt-BR', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}`;
                })()}
              </div>

              {/* Botões de ação sempre à direita */}
              <div className="flex space-x-1">
                {/* Botão copiar - para ambos */}
                <button
                  onClick={() => navigator.clipboard.writeText(message.text)}
                  className="bg-theme-primary hover:bg-theme-secondary text-theme-primary rounded-full p-1.5 shadow-md border border-theme transition-colors"
                  title="Copiar mensagem"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>

                {/* Botão editar - apenas para usuário */}
                {isUser && (
                  <button
                    onClick={() => onStartEdit(message.id, message.text)}
                    className="bg-theme-primary hover:bg-theme-secondary text-theme-primary rounded-full p-1.5 shadow-md border border-theme transition-colors"
                    title="Editar mensagem"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                )}

                {/* Botão excluir - para ambos */}
                <button
                  onClick={() => onDelete(message.id)}
                  className="bg-red-500 hover:bg-red-600 text-white rounded-full p-1.5 shadow-md transition-colors"
                  title="Excluir mensagem"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatBubble;
