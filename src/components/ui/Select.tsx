import React, { forwardRef } from 'react';

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'value'> {
  options: SelectOption[];
  label?: string;
  error?: string;
  helperText?: string;
  value?: string;
  isFullWidth?: boolean;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(({
  options,
  label,
  error,
  helperText,
  value,
  isFullWidth = true,
  className = '',
  id,
  ...props
}, ref) => {
  // Gera um ID aleatório se não for fornecido
  const selectId = id || `select-${Math.random().toString(36).substring(2, 11)}`;
  
  // Classes base para o select usando sistema de temas
  const baseSelectClasses = 'input-theme block rounded-md shadow-sm py-2 pl-3 pr-10 text-base focus:outline-none focus:ring-1 sm:text-sm';

  // Classes adicionais baseadas em props
  const selectStateClasses = error
    ? 'input-error'
    : '';
  
  // Classes de largura
  const widthClasses = isFullWidth ? 'w-full' : '';
  
  // Classes dinâmicas combinadas
  const selectClasses = `
    ${baseSelectClasses}
    ${selectStateClasses}
    ${widthClasses}
    ${className}
  `;
  
  return (
    <div className={`${isFullWidth ? 'w-full' : ''} mb-4`}>
      {label && (
        <label
          htmlFor={selectId}
          className="block text-sm font-medium text-theme-primary mb-1"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        <select
          ref={ref}
          id={selectId}
          className={selectClasses}
          value={value}
          aria-invalid={!!error}
          aria-describedby={error ? `${selectId}-error` : helperText ? `${selectId}-helper-text` : undefined}
          {...props}
        >
          {options.map((option) => (
            <option 
              key={option.value} 
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
      
      {error ? (
        <p id={`${selectId}-error`} className="mt-1 text-sm text-red-600">
          {error}
        </p>
      ) : helperText ? (
        <p id={`${selectId}-helper-text`} className="mt-1 text-sm text-gray-500">
          {helperText}
        </p>
      ) : null}
    </div>
  );
});

Select.displayName = 'Select';

export default Select;
