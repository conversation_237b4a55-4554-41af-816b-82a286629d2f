import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  footer?: React.ReactNode;
  isHoverable?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  className = '',
  title,
  footer,
  isHoverable = false,
}) => {
  const baseClasses = 'card-theme rounded-lg overflow-hidden';
  const hoverClasses = isHoverable ? 'cursor-pointer' : '';
  
  return (
    <div className={`${baseClasses} ${hoverClasses} ${className}`}>
      {title && (
        <div className="px-4 py-3 border-b border-theme bg-theme-secondary">
          <h3 className="text-lg font-medium text-theme-primary">{title}</h3>
        </div>
      )}
      
      <div className="p-4">
        {children}
      </div>
      
      {footer && (
        <div className="px-4 py-3 bg-theme-secondary border-t border-theme">
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;
