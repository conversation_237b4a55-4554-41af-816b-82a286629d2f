import React, { forwardRef } from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  isLoading?: boolean;
  placeholder?: string;
}

export const ChatInput = forwardRef<HTMLTextAreaElement, ChatInputProps>(({
  value,
  onChange,
  onSend,
  onKeyDown,
  disabled = false,
  isLoading = false,
  placeholder = "Digite sua mensagem...",
}, ref) => {
  return (
    <div className="border-t border-theme bg-theme-secondary p-3">
      <div className="max-w-4xl mx-auto">
        <Card className="card-theme">
          <div className="flex items-end space-x-2 p-3">
            <textarea
              ref={ref}
              className="flex-1 resize-none border-0 bg-transparent py-1 px-0 focus:ring-0 focus:outline-none min-h-[36px] max-h-[120px] text-sm text-theme-primary placeholder-theme-muted"
              placeholder={placeholder}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={onKeyDown}
              disabled={disabled}
            />
            <Button
              onClick={onSend}
              isLoading={isLoading}
              disabled={!value.trim() || disabled}
              size="sm"
              className="flex items-center space-x-1 px-3"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              <span className="hidden sm:inline">Enviar</span>
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
});

ChatInput.displayName = 'ChatInput';

export default ChatInput;
