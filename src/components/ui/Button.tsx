import React from 'react';

// Tipos de variantes do botão
export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'link' | 'error' | 'success' | 'warning' | 'accent';
export type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isFullWidth?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  isFullWidth = false,
  isLoading = false,
  leftIcon,
  rightIcon,
  children,
  className = '',
  disabled,
  ...props
}) => {
  // Classes base usando o sistema de temas
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus-visible touch-target';
  
  // Classes específicas de variante usando sistema de temas
  const variantClasses = {
    primary: 'btn-primary disabled:opacity-50',
    secondary: 'btn-secondary disabled:opacity-50',
    accent: 'btn-accent disabled:opacity-50',
    success: 'btn-success disabled:opacity-50',
    warning: 'btn-warning disabled:opacity-50',
    error: 'btn-error disabled:opacity-50',
    outline: 'border border-theme bg-transparent text-theme-primary hover:bg-theme-secondary disabled:opacity-50',
    ghost: 'bg-transparent text-theme-primary hover:bg-theme-secondary disabled:opacity-50',
    link: 'bg-transparent text-theme-primary hover:underline p-0 h-auto disabled:opacity-50',
  };
  
  // Classes específicas de tamanho
  const sizeClasses = {
    sm: 'text-sm px-3 py-1.5',
    md: 'text-base px-4 py-2',
    lg: 'text-lg px-6 py-3',
  };
  
  // Classes específicas de largura
  const widthClasses = isFullWidth ? 'w-full' : '';
  
  // Classes dinâmicas combinadas
  const buttonClasses = `
    ${baseClasses}
    ${variantClasses[variant]}
    ${variant !== 'link' ? sizeClasses[size] : ''}
    ${widthClasses}
    ${isLoading ? 'opacity-80 cursor-wait' : ''}
    ${className}
  `;
  
  return (
    <button
      className={buttonClasses}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      
      {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      {children}
      {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </button>
  );
};

export default Button;
