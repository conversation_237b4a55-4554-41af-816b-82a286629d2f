import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import { FlowService, FlowConfig } from '@/services/flowService';
import { FlowStorageAdapter } from '@/services/flow/flowStorageAdapter';
import { ChatMessage } from '@/types';
import { useAuth } from './AuthProvider';

interface FlowContextType {
  flowService: FlowService | null;
  isConfigured: boolean;
  isConfigurationComplete: boolean;
  loading: boolean;
  error: string | null;
  models: any[];
  selectedModel: string;
  selectedModelId: string;
  showConfigModal: boolean;
  setShowConfigModal: (show: boolean) => void;
  saveConfig: (config: FlowConfig) => Promise<void>;
  setSelectedModel: (modelId: string) => void;
  getModelOptions: () => Promise<any[]>;
  createChatCompletion: (messages: ChatMessage[], modelId?: string) => Promise<string>;
  saveChatSession: (messages: ChatMessage[]) => Promise<void>;
  loadChatSessions: () => Promise<any[]>;
  loadUserSessions: () => Promise<any[]>;
  createSession: () => Promise<{ id: string }>;
  // Novas funções para ChatPage
  messages: ChatMessage[];
  currentSession: FlowSession | null;
  loadingResponse: boolean;
  loadSession: (sessionId: string) => Promise<FlowSession | null>;
  sendMessage: (content: string) => Promise<void>;
  renameSession: (sessionId: string, newName: string) => Promise<void>;
  changeSessionModel: (sessionId: string, newModelId: string) => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string) => Promise<void>;
}

const FlowContext = createContext<FlowContextType | undefined>(undefined);

export const useFlow = (): FlowContextType => {
  const context = useContext(FlowContext);
  if (!context) {
    throw new Error('useFlow deve ser usado dentro de um FlowProvider');
  }
  return context;
};

export const FlowProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [flowService, setFlowService] = useState<FlowService | null>(null);
  const [flowStorageService] = useState<FlowStorageAdapter>(new FlowStorageAdapter());
  const [isConfigured, setIsConfigured] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [models, setModels] = useState<any[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [showConfigModal, setShowConfigModal] = useState<boolean>(false);
  // Estados para ChatPage
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentSession, setCurrentSession] = useState<FlowSession | null>(null);
  const [loadingResponse, setLoadingResponse] = useState<boolean>(false);

  // Função para verificar se a configuração está completa (campos obrigatórios)
  const isConfigurationComplete = (config: FlowConfig | null): boolean => {
    if (!config) return false;
    return !!(config.flowTenant && config.flowClientId && config.flowClientSecret);
  };

  // Carrega as configurações do Flow do Firebase quando o usuário fizer login
  useEffect(() => {
    const loadFlowConfig = async () => {
      if (user) {
        try {
          setLoading(true);
          setError(null);
          
          const flowConfig = await flowStorageService.getFlowConfig(user.uid);

          if (flowConfig && isConfigurationComplete(flowConfig)) {
            initFlowService(flowConfig);

            // Garantir que temos um modelo válido
            const modelId = flowConfig.apiModelId || "gpt-4o-mini";
            if (modelId && !modelId.includes("unknown")) {
              setSelectedModel(modelId);
            } else {
              setSelectedModel("gpt-4o-mini");
              console.warn("⚠️ [FlowProvider] Modelo inválido na config, usando fallback: gpt-4o-mini");
            }
          } else {
            // Se não houver configuração salva ou estiver incompleta, tenta usar variáveis de ambiente
            const envConfig = getEnvConfig();

            if (envConfig && isConfigurationComplete(envConfig)) {
              initFlowService(envConfig);
            } else {
              setIsConfigured(false);
            }
          }
        } catch (err) {
          console.error("❌ [FlowProvider] Erro ao carregar configurações:", err);
          setError(err instanceof Error ? err.message : "Erro ao carregar configurações");
          setIsConfigured(false);
        } finally {
          setLoading(false);
        }
      } else {
        console.log("👤 [FlowProvider] Usuário não logado, resetando configurações");
        setFlowService(null);
        setIsConfigured(false);
      }
    };

    loadFlowConfig();
  }, [user]);

  // Obtém configuração das variáveis de ambiente
  const getEnvConfig = (): FlowConfig | null => {
    const baseUrl = import.meta.env.VITE_FLOW_BASE_URL;
    const tenant = import.meta.env.VITE_FLOW_TENANT;
    const clientId = import.meta.env.VITE_FLOW_CLIENT_ID;
    const clientSecret = import.meta.env.VITE_FLOW_CLIENT_SECRET;
    
    if (baseUrl && tenant && clientId && clientSecret) {
      return {
        flowBaseUrl: baseUrl,
        flowTenant: tenant,
        flowClientId: clientId,
        flowClientSecret: clientSecret,
        flowAppToAccess: import.meta.env.VITE_FLOW_APP_TO_ACCESS || "llm-api"
      };
    }
    
    return null;
  };

  // Inicializa o serviço Flow com as configurações
  const initFlowService = (config: FlowConfig) => {
    try {
      console.log("🔧 [FlowProvider] Inicializando FlowService com config:", {
        hasBaseUrl: !!config.flowBaseUrl,
        hasTenant: !!config.flowTenant,
        hasClientId: !!config.flowClientId,
        hasClientSecret: !!config.flowClientSecret
      });
      
      const service = new FlowService(config);
      setFlowService(service);
      setIsConfigured(true);
      
      console.log("✅ [FlowProvider] FlowService inicializado com sucesso");
      
      // Carrega os modelos disponíveis de forma assíncrona
      loadModels(service).catch(err => {
        console.warn("⚠️ [FlowProvider] Erro ao carregar modelos (não afeta funcionamento):", err);
      });

      // Corrigir sessões com modelos inválidos
      fixInvalidModelSessions().catch(err => {
        console.warn("⚠️ [FlowProvider] Erro ao corrigir sessões com modelos inválidos:", err);
      });
    } catch (err) {
      console.error("❌ [FlowProvider] Erro ao inicializar FlowService:", err);
      setError(err instanceof Error ? err.message : "Erro ao inicializar o serviço Flow");
      setIsConfigured(false);
    }
  };

  // Corrige sessões existentes com modelos inválidos
  const fixInvalidModelSessions = async () => {
    if (!user) return;

    try {
      console.log("🔧 [FlowProvider] Verificando sessões com modelos inválidos...");
      const sessions = await flowStorageService.getSessionsByUser(user.uid, 50); // Buscar até 50 sessões

      let fixedCount = 0;
      for (const session of sessions) {
        if (!session.modelId || session.modelId.includes('unknown') || session.modelId === '') {
          console.log(`🔄 [FlowProvider] Corrigindo sessão ${session.id} com modelo inválido: ${session.modelId}`);
          await flowStorageService.updateSession(session.id, {
            modelId: 'gpt-4o-mini'
          });
          fixedCount++;
        }
      }

      if (fixedCount > 0) {
        console.log(`✅ [FlowProvider] ${fixedCount} sessões corrigidas com modelo válido`);
      } else {
        console.log("✅ [FlowProvider] Nenhuma sessão com modelo inválido encontrada");
      }
    } catch (err) {
      console.error("❌ [FlowProvider] Erro ao corrigir sessões com modelos inválidos:", err);
    }
  };

  // Carrega modelos disponíveis
  const loadModels = async (service: FlowService) => {
    try {
      // Carrega modelos de todos os provedores disponíveis
      const providers = ["azure-openai", "google-gemini", "amazon-bedrock", "azure-foundry"];
      let allModels: any[] = [];

      for (const provider of providers) {
        try {
          const providerModels = await service.listModels(provider);

          // Adicionar informação do provider a cada modelo
          const modelsWithProvider = providerModels.map(model => ({
            ...model,
            provider: provider,
            // Garantir que temos um ID válido
            id: model.id || model.name || model.modelId,
            name: model.name || model.displayName || model.id
          }));

          allModels = [...allModels, ...modelsWithProvider];
        } catch (err) {
          console.warn(`⚠️ [FlowProvider] Erro ao carregar modelos do ${provider}:`, err);
          // Continua mesmo se falhar para um provedor
        }
      }

      setModels(allModels);
    } catch (err) {
      console.error("❌ [FlowProvider] Erro geral ao carregar modelos:", err);
      // Não definir erro aqui para não afetar o isConfigured
      // setError(err instanceof Error ? err.message : "Erro ao carregar modelos");
    }
  };

  // Salva a configuração do Flow
  const saveConfig = async (config: FlowConfig): Promise<void> => {
    try {
      if (!user) {
        throw new Error("Usuário não autenticado");
      }
      
      setLoading(true);
      setError(null);
      
      await flowStorageService.saveFlowConfig(user.uid, config);
      initFlowService(config);
      
      setLoading(false);
    } catch (err) {
      setLoading(false);
      setError(err instanceof Error ? err.message : "Erro ao salvar configuração");
      throw err;
    }
  };

  // Obtém opções de modelos formatadas para UI
  const getModelOptions = async (): Promise<any[]> => {
    if (!flowService) {
      return [];
    }
    
    try {
      const modelsList = models.length > 0 ? models : await flowService.listModels();
      return modelsList.map((model: any) => ({
        value: model.id,
        label: `${model.name} (${model.provider})`,
      }));
    } catch (err) {
      console.error("Erro ao obter opções de modelos:", err);
      return [];
    }
  };

  // Cria chat completions usando o Flow
  const createChatCompletion = async (messages: ChatMessage[], modelId?: string): Promise<string> => {
    if (!flowService) {
      throw new Error("Serviço Flow não configurado");
    }
    
    try {
      // Se temos uma sessão atual, salvamos as mensagens
      if (currentSessionId && user) {
        await flowStorageService.addMessageToSession(currentSessionId, messages[messages.length - 1]);
      } else if (user) {
        // Se não temos uma sessão, criamos uma nova
        const config = await flowStorageService.getFlowConfig(user.uid);
        if (config) {
          const session = await flowStorageService.saveSession({
            userId: user.uid,
            modelId: modelId || selectedModel,
            lastUsedAt: new Date(),
            flowConfig: config,
            messages: []
          });
          setCurrentSessionId(session.id);

          // Salvamos todas as mensagens na nova sessão
          const sessionMessages = [...messages];
          await flowStorageService.updateSession(session.id, { messages: sessionMessages });
        }
      }
      
      const response = await flowService.createChatCompletion(messages, modelId || selectedModel);
      return response;
    } catch (err) {
      console.error("Erro ao criar chat completion:", err);
      throw err;
    }
  };

  // Salva uma sessão de chat
  const saveChatSession = async (messages: ChatMessage[]): Promise<void> => {
    if (!user) {
      throw new Error("Usuário não autenticado");
    }
    
    try {
      if (currentSessionId) {
        await flowStorageService.updateSession(currentSessionId, { messages });
      } else {
        const config = await flowStorageService.getFlowConfig(user.uid);
        if (config) {
          const session = await flowStorageService.saveSession({
            userId: user.uid,
            modelId: selectedModel,
            lastUsedAt: new Date(),
            flowConfig: config,
            messages: []
          });
          setCurrentSessionId(session.id);
          await flowStorageService.updateSession(session.id, { messages });
        }
      }
    } catch (err) {
      console.error("Erro ao salvar sessão de chat:", err);
      throw err;
    }
  };

  // Carrega as sessões de chat do usuário
  const loadChatSessions = async (): Promise<any[]> => {
    if (!user) {
      return [];
    }

    try {
      const sessions = await flowStorageService.getSessionsByUser(user.uid);
      return sessions.sort((a, b) => b.lastUsedAt.getTime() - a.lastUsedAt.getTime());
    } catch (err) {
      console.error("Erro ao carregar sessões de chat:", err);
      return [];
    }
  };

  // Alias for loadChatSessions to match HomePage usage
  const loadUserSessions = loadChatSessions;

  // Cria uma nova sessão
  const createSession = useCallback(async (): Promise<{ id: string }> => {
    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    try {
      const config = await flowStorageService.getFlowConfig(user.uid);
      if (!config) {
        throw new Error("Configuração Flow não encontrada");
      }

      // Garantir que temos um modelo válido
      let modelToUse = selectedModel;
      if (!modelToUse || modelToUse === "" || modelToUse.includes("unknown")) {
        modelToUse = "gpt-4o-mini"; // Modelo padrão confiável
        setSelectedModel(modelToUse); // Atualizar o estado também
        console.warn("⚠️ [FlowProvider] Modelo inválido na criação de sessão, usando fallback:", modelToUse);
      }

      const session = await flowStorageService.saveSession({
        userId: user.uid,
        modelId: modelToUse,
        lastUsedAt: new Date(),
        flowConfig: config,
        messages: []
      });

      // Definir como sessão atual
      setCurrentSessionId(session.id);
      setCurrentSession(session);
      setMessages(session.messages || []);



      return { id: session.id };
    } catch (err) {
      console.error("Erro ao criar sessão:", err);
      throw err;
    }
  }, [user, selectedModel, flowStorageService]);

  // Carrega uma sessão específica
  const loadSession = useCallback(async (sessionId: string): Promise<FlowSession | null> => {
    try {
      const session = await flowStorageService.getSession(sessionId);
      if (session) {
        setCurrentSession(session);
        setMessages(session.messages || []);
        setCurrentSessionId(session.id);
      }
      return session;
    } catch (error) {
      console.error('Erro ao carregar sessão:', error);
      throw error;
    }
  }, [flowStorageService]);

  // Envia uma mensagem
  const sendMessage = async (content: string): Promise<void> => {
    if (!flowService || !user) {
      const errorMsg = !user ? 'Usuário não autenticado' : 'Serviço Flow não configurado';
      console.error("❌ [FlowProvider] Erro:", errorMsg);
      throw new Error(errorMsg);
    }

    if (!isConfigured) {
      console.error("❌ [FlowProvider] Flow não está configurado");
      throw new Error('Flow não está configurado. Verifique suas configurações.');
    }

    try {
      setLoadingResponse(true);

      // Criar mensagem do usuário
      const userMessage: ChatMessage = {
        id: crypto.randomUUID(),
        role: 'user',
        text: content,
        timestamp: new Date()
      };

      // Atualizar mensagens localmente
      const newMessages = [...messages, userMessage];
      setMessages(newMessages);

      // Salvar mensagem do usuário na sessão
      if (currentSessionId) {
        await flowStorageService.addMessageToSession(currentSessionId, userMessage);
      }

      // Determinar qual modelo usar (sessão atual ou modelo selecionado globalmente)
      let modelToUse = currentSession?.modelId || selectedModel;

      // Fallback para modelo padrão se não temos um modelo válido
      if (!modelToUse || modelToUse === "" || modelToUse.includes("unknown")) {
        modelToUse = "gpt-4o-mini"; // Modelo padrão confiável
        console.warn("⚠️ [FlowProvider] Modelo inválido detectado, usando fallback:", modelToUse);
      }



      // Verificar se precisa adicionar prompt do sistema (primeira mensagem da conversa)
      let messagesForAPI = newMessages;
      const hasSystemMessage = newMessages.some(m => m.role === 'system');
      
      if (!hasSystemMessage && newMessages.filter(m => m.role !== 'system').length === 1) {
        // É a primeira mensagem do usuário, adicionar prompt do sistema
        const systemMessage: ChatMessage = {
          id: crypto.randomUUID(),
          role: 'system',
          text: 'Você é FlowEd, um assistente cognitivo adaptável. Seja útil, claro e gentil.',
          timestamp: new Date()
        };
        
        messagesForAPI = [systemMessage, ...newMessages];
        
        // Salvar a mensagem do sistema na sessão também
        if (currentSessionId) {
          await flowStorageService.addMessageToSession(currentSessionId, systemMessage);
        }
        
        // Atualizar estado local para incluir a mensagem do sistema
        setMessages(messagesForAPI);
      }

      // Obter resposta do assistente usando o modelo correto
      const response = await flowService.createChatCompletion(messagesForAPI, modelToUse);

      // Criar mensagem do assistente
      const assistantMessage: ChatMessage = {
        id: crypto.randomUUID(),
        role: 'assistant',
        text: response,
        timestamp: new Date()
      };

      // Atualizar mensagens com resposta do assistente
      // Se adicionamos uma mensagem do sistema, usar messagesForAPI, senão usar newMessages
      const finalMessages = messagesForAPI.length > newMessages.length
        ? [...messagesForAPI, assistantMessage] // Inclui sistema + usuário + assistente
        : [...newMessages, assistantMessage]; // Inclui apenas usuário + assistente
      setMessages(finalMessages);



      // Salvar mensagem do assistente na sessão
      if (currentSessionId) {
        await flowStorageService.addMessageToSession(currentSessionId, assistantMessage);
      }

      // Atualizar sessão atual para manter sincronização
      if (currentSession) {
        setCurrentSession({
          ...currentSession,
          messages: finalMessages,
          lastUsedAt: new Date()
        });
      }

    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      throw error;
    } finally {
      setLoadingResponse(false);
    }
  };

  // Renomeia uma sessão
  const renameSession = async (sessionId: string, newName: string): Promise<void> => {
    try {
      await flowStorageService.updateSession(sessionId, { name: newName });

      // Atualizar sessão atual se for a mesma
      if (currentSession && currentSession.id === sessionId) {
        setCurrentSession({ ...currentSession, name: newName });
      }

      console.log(`Sessão ${sessionId} renomeada para: "${newName}"`);
    } catch (error) {
      console.error('Erro ao renomear sessão:', error);
      throw error;
    }
  };

  // Troca o modelo de uma sessão
  const changeSessionModel = async (sessionId: string, newModelId: string): Promise<void> => {
    try {
      await flowStorageService.updateSession(sessionId, { modelId: newModelId });

      // Atualizar sessão atual se for a mesma
      if (currentSession && currentSession.id === sessionId) {
        setCurrentSession({ ...currentSession, modelId: newModelId });
      }

      // Atualizar modelo selecionado globalmente
      setSelectedModel(newModelId);
    } catch (error) {
      console.error('Erro ao trocar modelo da sessão:', error);
      throw error;
    }
  };

  // Edita uma mensagem e remove todas as mensagens posteriores
  const editMessage = async (messageId: string, newContent: string): Promise<void> => {
    if (!currentSession) {
      throw new Error('Nenhuma sessão ativa');
    }

    try {
      // Encontrar o índice da mensagem a ser editada
      const messageIndex = messages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        throw new Error('Mensagem não encontrada');
      }

      // Criar nova lista de mensagens até a mensagem editada (inclusive)
      const updatedMessages = messages.slice(0, messageIndex + 1);

      // Atualizar o conteúdo da mensagem editada
      updatedMessages[messageIndex] = {
        ...updatedMessages[messageIndex],
        text: newContent,
        timestamp: new Date()
      };

      // Atualizar estado local
      setMessages(updatedMessages);

      // Salvar no Firestore
      await flowStorageService.updateSession(currentSession.id, {
        messages: updatedMessages,
        lastUsedAt: new Date()
      });

      // Atualizar sessão atual
      setCurrentSession({
        ...currentSession,
        messages: updatedMessages,
        lastUsedAt: new Date()
      });

      console.log(`✏️ [FlowProvider] Mensagem editada e ${messages.length - updatedMessages.length} mensagens posteriores removidas`);
    } catch (error) {
      console.error('Erro ao editar mensagem:', error);
      throw error;
    }
  };

  // Deleta uma mensagem e todas as mensagens posteriores
  const deleteMessage = async (messageId: string): Promise<void> => {
    if (!currentSession) {
      throw new Error('Nenhuma sessão ativa');
    }

    try {
      // Encontrar o índice da mensagem a ser deletada
      const messageIndex = messages.findIndex(msg => msg.id === messageId);
      if (messageIndex === -1) {
        throw new Error('Mensagem não encontrada');
      }

      // Criar nova lista de mensagens até antes da mensagem deletada
      const updatedMessages = messages.slice(0, messageIndex);

      // Atualizar estado local
      setMessages(updatedMessages);

      // Salvar no Firestore
      await flowStorageService.updateSession(currentSession.id, {
        messages: updatedMessages,
        lastUsedAt: new Date()
      });

      // Atualizar sessão atual
      setCurrentSession({
        ...currentSession,
        messages: updatedMessages,
        lastUsedAt: new Date()
      });

      console.log(`🗑️ [FlowProvider] Mensagem deletada e ${messages.length - updatedMessages.length} mensagens posteriores removidas`);
    } catch (error) {
      console.error('Erro ao deletar mensagem:', error);
      throw error;
    }
  };

  const value = {
    flowService,
    isConfigured,
    isConfigurationComplete: (config: FlowConfig | null) => isConfigurationComplete(config),
    loading,
    error,
    models,
    selectedModel,
    selectedModelId: selectedModel,
    showConfigModal,
    setShowConfigModal,
    saveConfig,
    setSelectedModel,
    getModelOptions,
    createChatCompletion,
    saveChatSession,
    loadChatSessions,
    loadUserSessions,
    createSession,
    // Novas propriedades para ChatPage
    messages,
    currentSession,
    loadingResponse,
    loadSession,
    sendMessage,
    renameSession,
    changeSessionModel,
    editMessage,
    deleteMessage
  };
  
  return (
    <FlowContext.Provider value={value}>
      {children}
    </FlowContext.Provider>
  );
};
