import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { ThemeMode } from '@/types/theme';

export const ThemeToggle: React.FC = () => {
  const { theme, effectiveTheme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const themeOptions: { value: ThemeMode; label: string; icon: string }[] = [
    {
      value: 'light',
      label: 'Claro',
      icon: '☀️'
    },
    {
      value: 'dark',
      label: 'Escuro',
      icon: '🌙'
    },
    {
      value: 'system',
      label: 'Sistema',
      icon: '💻'
    }
  ];

  const currentOption = themeOptions.find(option => option.value === theme) || themeOptions[2];

  const handleThemeSelect = (newTheme: ThemeMode) => {
    setTheme(newTheme);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {/* Botão principal */}
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-lg bg-theme-secondary/50 border border-theme hover:bg-theme-tertiary transition-all duration-200"
        aria-label="Seletor de tema"
      >
        <div className="flex items-center space-x-2">
          <span className="text-lg">{currentOption.icon}</span>
          <span className="text-sm font-medium text-theme-primary hidden sm:block">
            {currentOption.label}
          </span>
          <svg
            className={`w-3 h-3 text-theme-secondary transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </Button>

      {/* Menu dropdown */}
      {isOpen && (
        <>
          {/* Overlay para fechar ao clicar fora */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* Menu de opções */}
          <div className="absolute top-full right-0 mt-2 w-48 bg-theme-primary border border-theme rounded-lg shadow-xl z-50 overflow-hidden">
            <div className="p-1">
              {themeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleThemeSelect(option.value)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors duration-150 ${
                    theme === option.value
                      ? 'bg-theme-tertiary text-theme-primary'
                      : 'hover:bg-theme-secondary text-theme-secondary hover:text-theme-primary'
                  }`}
                >
                  <span className="text-lg">{option.icon}</span>
                  <span className="font-medium text-sm">{option.label}</span>
                  {theme === option.value && (
                    <svg className="w-4 h-4 text-theme-primary ml-auto" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>

            {/* Indicador do tema atual */}
            <div className="border-t border-theme px-3 py-2 bg-theme-secondary">
              <div className="text-xs text-theme-muted">
                <span className="font-medium">Atual:</span> {effectiveTheme === 'dark' ? 'Escuro' : 'Claro'}
                {theme === 'system' && <span className="ml-1">(auto)</span>}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};
