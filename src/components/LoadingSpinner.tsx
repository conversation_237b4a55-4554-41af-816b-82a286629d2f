
import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string; // Tailwind text color class e.g. text-blue-500
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md', color = 'text-blue-500' }) => {
  let spinnerSizeClass = 'h-8 w-8';
  if (size === 'sm') spinnerSizeClass = 'h-5 w-5';
  if (size === 'lg') spinnerSizeClass = 'h-12 w-12';

  // Ensure border color for spinner uses the currentColor to match text color utility
  const borderColorClass = color.startsWith('text-') ? color.replace('text-', 'border-') : `border-${color}`;


  return (
    <div
      className={`animate-spin rounded-full ${spinnerSizeClass} border-t-2 border-b-2 ${borderColorClass} border-opacity-50`}
      style={{ borderTopColor: 'currentColor', borderBottomColor: 'currentColor' }} 
    ></div>
  );
};