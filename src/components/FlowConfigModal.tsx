import React, { useState } from 'react';
import { useFlow } from './FlowProvider';
import { FlowConfig } from '@/services/flowService';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';
import { Card } from '@/components/ui/Card';

interface FlowConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const FlowConfigModal: React.FC<FlowConfigModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess 
}) => {
  const { saveConfig, loading, error } = useFlow();
  const [formData, setFormData] = useState<FlowConfig>({
    flowBaseUrl: import.meta.env.VITE_FLOW_BASE_URL || 'https://flow.ciandt.com',
    flowTenant: import.meta.env.VITE_FLOW_TENANT || 'cit',
    flowClientId: import.meta.env.VITE_FLOW_CLIENT_ID || '0730f539-b08b-419b-a2f4-b07becad0c9a',
    flowClientSecret: import.meta.env.VITE_FLOW_CLIENT_SECRET || '',
    flowAppToAccess: import.meta.env.VITE_FLOW_APP_TO_ACCESS || 'llm-api',
    flowAgent: 'chat',
    modelTemperature: 0.7
  });
  const [success, setSuccess] = useState<boolean>(false);
  const [validating, setValidating] = useState<boolean>(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [validationSuccess, setValidationSuccess] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);
  const [testing, setTesting] = useState<boolean>(false);
  const [hideFlowError, setHideFlowError] = useState<boolean>(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'modelTemperature' ? parseFloat(value) : value
    }));
    setSuccess(false);
    setValidationSuccess(null);
    setValidationError(null);
    setHideFlowError(false);
  };

  const validateCredentials = async (): Promise<boolean> => {
    setValidating(true);
    setValidationError(null);
    setValidationSuccess(null);

    try {
      const { FlowService } = await import('../services/flowService');
      const tempService = new FlowService(formData);
      await tempService.listModels();

      setValidating(false);
      return true;
    } catch (err) {
      console.error("Erro ao validar credenciais:", err);
      setValidationError(err instanceof Error ? err.message : 'Falha ao validar as credenciais do Flow');
      setValidating(false);
      return false;
    }
  };

  const handleTestConnection = async () => {
    setTesting(true);
    setValidationError(null);
    setValidationSuccess(null);

    try {
      const { FlowService } = await import('../services/flowService');
      const tempService = new FlowService(formData);
      const models = await tempService.listModels();

      setValidationSuccess(`Conexão bem-sucedida! Encontrados ${models.length} modelos disponíveis.`);
      setTesting(false);
    } catch (err) {
      console.error("Erro ao testar conexão:", err);
      setValidationError(err instanceof Error ? err.message : 'Falha ao conectar com o Flow');
      setTesting(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const isValid = await validateCredentials();
      if (!isValid) {
        return;
      }
      
      await saveConfig(formData);
      setSuccess(true);
      
      // Aguarda um pouco para mostrar o sucesso, depois chama callbacks
      setTimeout(() => {
        onSuccess?.();
        onClose();
      }, 1500);
    } catch (err) {
      console.error("Erro ao salvar configuração:", err);
    }
  };

  const handleClose = () => {
    if (!loading && !validating && !testing) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-2">
        <Card className="relative w-full max-w-xl card-theme shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-theme">
            <h2 className="text-lg font-semibold text-theme-primary">
              🔧 Configuração do Flow
            </h2>
            <button
              onClick={handleClose}
              disabled={loading || validating || testing}
              className="text-theme-secondary hover:text-theme-primary transition-colors disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-4">
            <div className="mb-3">
              <p className="text-theme-secondary text-xs">
                Configure suas credenciais do Flow para começar a usar a plataforma.
              </p>
            </div>

            {success && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="success" title="Sucesso!">
                    Configuração salva com sucesso!
                  </Alert>
                  <button
                    onClick={() => setSuccess(false)}
                    className="absolute top-2 right-2 text-green-600 hover:text-green-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {error && !hideFlowError && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="error" title="Erro">
                    {error}
                  </Alert>
                  <button
                    onClick={() => setHideFlowError(true)}
                    className="absolute top-2 right-2 text-red-600 hover:text-red-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {validationError && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="warning" title="Erro de Conexão">
                    {validationError}
                  </Alert>
                  <button
                    onClick={() => setValidationError(null)}
                    className="absolute top-2 right-2 text-orange-600 hover:text-orange-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            {validationSuccess && (
              <div className="mb-3">
                <div className="relative">
                  <Alert variant="success" title="Teste de Conexão">
                    {validationSuccess}
                  </Alert>
                  <button
                    onClick={() => setValidationSuccess(null)}
                    className="absolute top-2 right-2 text-green-600 hover:text-green-800 transition-colors"
                    aria-label="Fechar"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>
            )}
            
            <form onSubmit={handleSubmit} className="space-y-3">
              {/* Campos básicos */}
              <div>
                <label htmlFor="flowTenant" className="block text-xs font-medium text-theme-primary mb-1">
                  Flow Tenant *
                </label>
                <input
                  type="text"
                  id="flowTenant"
                  name="flowTenant"
                  value={formData.flowTenant}
                  onChange={handleChange}
                  className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                  required
                  placeholder="ex: cit"
                />
              </div>
              
              <div>
                <label htmlFor="flowClientId" className="block text-xs font-medium text-theme-primary mb-1">
                  ID do Cliente *
                </label>
                <input
                  type="text"
                  id="flowClientId"
                  name="flowClientId"
                  value={formData.flowClientId}
                  onChange={handleChange}
                  className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                  required
                />
              </div>
              
              <div>
                <label htmlFor="flowClientSecret" className="block text-xs font-medium text-theme-primary mb-1">
                  Chave Secreta *
                </label>
                <input
                  type="password"
                  id="flowClientSecret"
                  name="flowClientSecret"
                  value={formData.flowClientSecret}
                  onChange={handleChange}
                  className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                  required
                />
              </div>
              
              {/* Configurações avançadas */}
              <div className="border-t border-theme pt-3">
                <button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="flex items-center justify-between w-full text-left text-xs font-medium text-theme-secondary hover:text-theme-primary focus:outline-none"
                >
                  <span>⚙️ Configurações Avançadas</span>
                  <svg
                    className={`w-3 h-3 transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                {showAdvanced && (
                  <div className="mt-3 space-y-3 pl-3 border-l-2 border-theme-accent">
                    <div>
                      <label htmlFor="flowBaseUrl" className="block text-xs font-medium text-theme-primary mb-1">
                        URL Base
                      </label>
                      <input
                        type="text"
                        id="flowBaseUrl"
                        name="flowBaseUrl"
                        value={formData.flowBaseUrl}
                        onChange={handleChange}
                        className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                        required
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="flowAppToAccess" className="block text-xs font-medium text-theme-primary mb-1">
                        Aplicação de Acesso
                      </label>
                      <input
                        type="text"
                        id="flowAppToAccess"
                        name="flowAppToAccess"
                        value={formData.flowAppToAccess}
                        onChange={handleChange}
                        className="w-full px-2 py-1.5 text-sm border border-theme rounded bg-theme-primary text-theme-primary focus:outline-none focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="modelTemperature" className="block text-xs font-medium text-theme-primary mb-1">
                        Temperatura: {formData.modelTemperature.toFixed(1)}
                      </label>
                      <input
                        type="range"
                        id="modelTemperature"
                        name="modelTemperature"
                        min="0"
                        max="1"
                        step="0.1"
                        value={formData.modelTemperature}
                        onChange={handleChange}
                        className="w-full h-1.5 bg-theme-secondary rounded-lg appearance-none cursor-pointer"
                      />
                      <div className="flex justify-between text-xs text-theme-muted mt-1">
                        <span>Previsível</span>
                        <span>Criativo</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-2 p-4 border-t border-theme">
            <Button
              variant="secondary"
              onClick={handleClose}
              disabled={loading || validating || testing}
              className="text-sm py-1.5 px-3"
            >
              Cancelar
            </Button>
            <Button
              variant="outline"
              onClick={handleTestConnection}
              isLoading={testing}
              disabled={loading || validating || testing || !formData.flowTenant || !formData.flowClientId || !formData.flowClientSecret}
              className="text-sm py-1.5 px-3"
            >
              {testing ? 'Testando...' : '🔗 Testar'}
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              isLoading={loading || validating}
              disabled={loading || validating || testing}
              className="text-sm py-1.5 px-3"
            >
              {validating ? 'Validando...' : loading ? 'Salvando...' : 'Salvar'}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};
