
import React, { useState, useEffect } from 'react';
import { UserConsent, CognitiveType } from '@/types'; 
import { CONSENT_OPTIONS_CONFIG } from '@/constants'; 
import { AdaptiveButton } from './ui/AdaptiveButton'; 

interface ConsentModalProps {
  initialConsents: UserConsent;
  onSave: (consents: UserConsent) => void;
  onClose: () => void;
}

export const ConsentModal: React.FC<ConsentModalProps> = ({ initialConsents, onSave, onClose }) => {
  const [consents, setConsents] = useState<UserConsent>(initialConsents);

  useEffect(() => {
    setConsents(initialConsents);
  }, [initialConsents]);

  const handleToggle = (key: keyof UserConsent) => {
    // Prevent unchecking a required field if it's already checked
    const optionConfig = CONSENT_OPTIONS_CONFIG.find(opt => opt.key === key);
    if (optionConfig?.required && consents[key]) {
      // User is trying to uncheck a required field that is currently checked - prevent this specific toggle action.
      // They must use "Reject Non-Essential" or it must be initially false for them to need to check it.
      return; 
    }
    setConsents(prev => ({ ...prev, [key]: !prev[key as keyof UserConsent] }));
  };

  const handleSave = () => {
    onSave({ ...consents, consentDate: new Date().toISOString() });
  };
  
  const handleSelectAll = (accept: boolean) => {
    const newConsents = { ...consents };
    CONSENT_OPTIONS_CONFIG.forEach(option => {
      if (typeof newConsents[option.key] === 'boolean') {
        if (option.required) {
          (newConsents[option.key] as boolean) = true; // Required options always true
        } else {
          (newConsents[option.key] as boolean) = accept; // Non-essential follow 'accept'
        }
      }
    });
    setConsents(newConsents);
  }

  const areAllRequiredConsented = () => {
    return CONSENT_OPTIONS_CONFIG.every(option => !option.required || consents[option.key]);
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-75 backdrop-blur-sm flex items-center justify-center z-50 p-4 animate-fadeIn">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 md:p-8 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white">Privacidade & Consentimento</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
            aria-label="Fechar modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <p className="text-gray-600 dark:text-gray-300 mb-6 text-sm md:text-base">
          Para lhe fornecer a melhor experiência, esta aplicação requer seu consentimento para certas funcionalidades. Você pode personalizar suas preferências abaixo. Suas escolhas são respeitadas.
        </p>

        <div className="space-y-5 mb-8">
          {CONSENT_OPTIONS_CONFIG.map((option) => (
            <div key={option.key} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-700/50 transition-shadow hover:shadow-md">
              <div className="flex items-center justify-between">
                <label htmlFor={option.key} className="font-semibold text-gray-700 dark:text-gray-200 text-base md:text-lg cursor-pointer">
                  {option.title}
                </label>
                <div className={`relative inline-block w-12 h-7 select-none transition duration-200 ease-in rounded-full ${consents[option.key] ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'}`}>
                   <input
                    type="checkbox"
                    id={option.key}
                    checked={consents[option.key] as boolean}
                    onChange={() => handleToggle(option.key)}
                    className="absolute top-0 left-0 w-full h-full opacity-0 cursor-pointer"
                    disabled={option.required && consents[option.key] as boolean} 
                  />
                  <span className={`absolute left-1 top-1 w-5 h-5 bg-white rounded-full shadow transform transition-transform duration-200 ease-in ${consents[option.key] ? 'translate-x-5' : ''}`}></span>
                </div>
              </div>
              <p className="text-gray-500 dark:text-gray-400 mt-1.5 text-xs md:text-sm">{option.description}</p>
              {option.required && <p className="text-red-500 text-xs mt-1 font-semibold">Este consentimento é necessário para a funcionalidade principal.</p>}
            </div>
          ))}
        </div>

        <div className="mb-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-700/50">
          <label htmlFor="dataRetentionDays" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
            Retenção de Dados Temporários:
          </label>
          <select
            id="dataRetentionDays"
            value={consents.dataRetentionDays}
            onChange={(e) => setConsents(prev => ({ ...prev, dataRetentionDays: parseInt(e.target.value) as (1 | 7 | 30) }))}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
          >
            <option value={1}>1 Dia</option>
            <option value={7}>7 Dias</option>
            <option value={30}>30 Dias</option>
          </select>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-center gap-3">
          <div className="flex gap-2">
             <AdaptiveButton onClick={() => handleSelectAll(true)} cognitiveType={CognitiveType.ADHD} variant="outline" size="sm">
                Aceitar Permitidos
             </AdaptiveButton>
             <AdaptiveButton onClick={() => handleSelectAll(false)} cognitiveType={CognitiveType.ADHD} variant="outline" size="sm">
                Rejeitar Não Essenciais
             </AdaptiveButton>
          </div>
          <AdaptiveButton onClick={handleSave} cognitiveType={CognitiveType.ADHD} variant="primary" size="lg" disabled={!areAllRequiredConsented()}>
            Salvar & Continuar
          </AdaptiveButton>
        </div>
         <p className="text-xs text-gray-500 dark:text-gray-400 mt-4 text-center">
            Suas escolhas de consentimento serão lembradas. {!areAllRequiredConsented() && <span className="text-red-500 font-semibold">Os consentimentos obrigatórios devem ser concedidos para continuar.</span>}
        </p>
      </div>
    </div>
  );
};
