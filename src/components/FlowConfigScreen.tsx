import React, { useState } from 'react';
import { useFlow } from './FlowProvider';
import { FlowConfig } from '@/services/flowService';
import { LoadingSpinner } from './LoadingSpinner';

export const FlowConfigScreen: React.FC = () => {
  const { saveConfig, loading, error } = useFlow();
  const [formData, setFormData] = useState<FlowConfig>({
    flowBaseUrl: import.meta.env.VITE_FLOW_BASE_URL || 'https://flow.ciandt.com',
    flowTenant: import.meta.env.VITE_FLOW_TENANT || 'cit',
    flowClientId: import.meta.env.VITE_FLOW_CLIENT_ID || '0730f539-b08b-419b-a2f4-b07becad0c9a',
    flowClientSecret: import.meta.env.VITE_FLOW_CLIENT_SECRET || '',
    flowAppToAccess: import.meta.env.VITE_FLOW_APP_TO_ACCESS || 'llm-api',
    flowAgent: 'chat',
    modelTemperature: 0.7
  });
  const [success, setSuccess] = useState<boolean>(false);
  const [validating, setValidating] = useState<boolean>(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'modelTemperature' ? parseFloat(value) : value
    }));
    setSuccess(false);
  };

  const validateCredentials = async (): Promise<boolean> => {
    setValidating(true);
    setValidationError(null);
    
    try {
      // Criamos uma instância temporária do FlowService para validar as credenciais
      const { FlowService } = await import('../services/flowService');
      const tempService = new FlowService(formData);
      
      // Tentamos listar os modelos para verificar se as credenciais estão corretas
      await tempService.listModels();
      
      setValidating(false);
      return true;
    } catch (err) {
      console.error("Erro ao validar credenciais:", err);
      setValidationError(err instanceof Error ? err.message : 'Falha ao validar as credenciais do Flow');
      setValidating(false);
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const isValid = await validateCredentials();
      if (!isValid) {
        return;
      }
      
      await saveConfig(formData);
      setSuccess(true);
    } catch (err) {
      console.error("Erro ao salvar configuração:", err);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Configuração do Flow Provider
        </h2>
        
        {success && (
          <div className="mb-4 p-3 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-md text-sm">
            Configuração salva com sucesso!
          </div>
        )}
        
        {error && (
          <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md text-sm">
            {error}
          </div>
        )}
        
        {validationError && (
          <div className="mb-4 p-3 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 rounded-md text-sm">
            {validationError}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="flowTenant" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Flow Tenant
            </label>
            <input
              type="text"
              id="flowTenant"
              name="flowTenant"
              value={formData.flowTenant}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              required
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Tenant do Flow (ex: cit)
            </p>
          </div>
          
          <div>
            <label htmlFor="flowClientId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Client ID
            </label>
            <input
              type="text"
              id="flowClientId"
              name="flowClientId"
              value={formData.flowClientId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          
          <div>
            <label htmlFor="flowClientSecret" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Client Secret
            </label>
            <input
              type="password"
              id="flowClientSecret"
              name="flowClientSecret"
              value={formData.flowClientSecret}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
              required
            />
          </div>
          
          {/* Advanced Settings Section */}
          <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center justify-between w-full text-left text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 focus:outline-none"
            >
              <span>Configurações Avançadas</span>
              <svg
                className={`w-4 h-4 transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            {showAdvanced && (
              <div className="mt-4 space-y-4 pl-4 border-l-2 border-gray-200 dark:border-gray-600">
                <div>
                  <label htmlFor="flowBaseUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    URL Base
                  </label>
                  <input
                    type="text"
                    id="flowBaseUrl"
                    name="flowBaseUrl"
                    value={formData.flowBaseUrl}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    required
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Padrão: https://flow.ciandt.com
                  </p>
                </div>
                
                <div>
                  <label htmlFor="flowAppToAccess" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    App to Access
                  </label>
                  <input
                    type="text"
                    id="flowAppToAccess"
                    name="flowAppToAccess"
                    value={formData.flowAppToAccess}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Padrão: llm-api
                  </p>
                </div>
                
                <div>
                  <label htmlFor="modelTemperature" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Temperatura do Modelo: {formData.modelTemperature.toFixed(1)}
                  </label>
                  <input
                    type="range"
                    id="modelTemperature"
                    name="modelTemperature"
                    min="0"
                    max="1"
                    step="0.1"
                    value={formData.modelTemperature}
                    onChange={handleChange}
                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>0.0 (Previsível)</span>
                    <span>1.0 (Criativo)</span>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="pt-4">
            <button
              type="submit"
              disabled={loading || validating}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400 disabled:cursor-not-allowed"
            >
              {loading || validating ? (
                <>
                  <LoadingSpinner size="sm" color="text-white" /> 
                  <span className="ml-2">
                    {validating ? 'Validando...' : 'Salvando...'}
                  </span>
                </>
              ) : (
                'Salvar Configuração'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
