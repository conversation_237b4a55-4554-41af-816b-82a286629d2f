import React, { useState, useEffect } from 'react';
import { marked } from 'marked';
import { Button } from '@/components/ui/Button';

interface MessageRendererProps {
  text: string;
  role: 'user' | 'assistant' | 'system';
  cognitiveProfile?: string;
}

interface Artifact {
  id: string;
  type: 'interactive-demo' | 'visual-diagram' | 'step-by-step' | 'quiz' | 'simulation';
  title: string;
  content: any;
}

export const MessageRenderer: React.FC<MessageRendererProps> = ({ 
  text, 
  role, 
  cognitiveProfile = 'default' 
}) => {
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [activeArtifact, setActiveArtifact] = useState<string | null>(null);

  // Configurar marked para segurança
  useEffect(() => {
    marked.setOptions({
      breaks: true,
      gfm: true,
      sanitize: false, // Vamos sanitizar manualmente se necessário
    });
  }, []);

  // Detectar e extrair artefatos do texto
  useEffect(() => {
    const artifactPattern = /\[ARTIFACT:(\w+):([^\]]+)\]([\s\S]*?)\[\/ARTIFACT\]/g;
    const foundArtifacts: Artifact[] = [];
    let match;

    while ((match = artifactPattern.exec(text)) !== null) {
      const [, type, title, content] = match;
      foundArtifacts.push({
        id: `artifact-${Date.now()}-${Math.random()}`,
        type: type as Artifact['type'],
        title,
        content: content.trim()
      });
    }

    setArtifacts(foundArtifacts);
  }, [text]);

  // Remover marcações de artefatos do texto para renderização
  const cleanText = text.replace(/\[ARTIFACT:(\w+):([^\]]+)\]([\s\S]*?)\[\/ARTIFACT\]/g, '');

  // Renderizar HTML do markdown
  const renderMarkdown = () => {
    try {
      const html = marked(cleanText);
      return { __html: html };
    } catch (error) {
      console.error('Erro ao renderizar markdown:', error);
      return { __html: cleanText };
    }
  };

  // Renderizar artefato interativo
  const renderArtifact = (artifact: Artifact) => {
    switch (artifact.type) {
      case 'interactive-demo':
        return <InteractiveDemo content={artifact.content} cognitiveProfile={cognitiveProfile} />;
      case 'visual-diagram':
        return <VisualDiagram content={artifact.content} cognitiveProfile={cognitiveProfile} />;
      case 'step-by-step':
        return <StepByStep content={artifact.content} cognitiveProfile={cognitiveProfile} />;
      case 'quiz':
        return <InteractiveQuiz content={artifact.content} cognitiveProfile={cognitiveProfile} />;
      case 'simulation':
        return <Simulation content={artifact.content} cognitiveProfile={cognitiveProfile} />;
      default:
        return <div className="p-4 bg-gray-100 rounded">{artifact.content}</div>;
    }
  };

  return (
    <div className="message-content">
      {/* Conteúdo principal em markdown */}
      <div 
        className="prose prose-sm max-w-none dark:prose-invert"
        dangerouslySetInnerHTML={renderMarkdown()}
      />

      {/* Artefatos interativos */}
      {artifacts.length > 0 && (
        <div className="mt-4 space-y-3">
          {artifacts.map((artifact) => (
            <div key={artifact.id} className="border rounded-lg overflow-hidden">
              <div className="bg-theme-secondary p-3 flex items-center justify-between">
                <h4 className="font-medium text-theme-primary">{artifact.title}</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setActiveArtifact(
                    activeArtifact === artifact.id ? null : artifact.id
                  )}
                >
                  {activeArtifact === artifact.id ? 'Fechar' : 'Abrir'}
                </Button>
              </div>
              {activeArtifact === artifact.id && (
                <div className="p-4 bg-white">
                  {renderArtifact(artifact)}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Componente para demonstração interativa
const InteractiveDemo: React.FC<{ content: string; cognitiveProfile: string }> = ({ 
  content, 
  cognitiveProfile 
}) => {
  const [step, setStep] = useState(0);
  
  try {
    const demo = JSON.parse(content);
    const steps = demo.steps || [];

    return (
      <div className="interactive-demo">
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">
              Passo {step + 1} de {steps.length}
            </span>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep(Math.max(0, step - 1))}
                disabled={step === 0}
              >
                Anterior
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep(Math.min(steps.length - 1, step + 1))}
                disabled={step === steps.length - 1}
              >
                Próximo
              </Button>
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-theme-accent h-2 rounded-full transition-all"
              style={{ width: `${((step + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>
        
        {steps[step] && (
          <div className="step-content">
            <h5 className="font-medium mb-2">{steps[step].title}</h5>
            <p className="text-gray-700 mb-3">{steps[step].description}</p>
            {steps[step].example && (
              <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-400">
                <strong>Exemplo:</strong> {steps[step].example}
              </div>
            )}
          </div>
        )}
      </div>
    );
  } catch (error) {
    return <div className="text-red-500">Erro ao carregar demonstração interativa</div>;
  }
};

// Componente para diagrama visual
const VisualDiagram: React.FC<{ content: string; cognitiveProfile: string }> = ({ 
  content, 
  cognitiveProfile 
}) => {
  try {
    const diagram = JSON.parse(content);
    
    return (
      <div className="visual-diagram">
        <div className="text-center mb-4">
          <h5 className="font-medium">{diagram.title}</h5>
        </div>
        
        <div className="flex flex-wrap justify-center items-center space-x-4 space-y-2">
          {diagram.elements?.map((element: any, index: number) => (
            <div key={index} className="flex flex-col items-center">
              <div className={`
                w-20 h-20 rounded-lg flex items-center justify-center text-white font-bold
                ${element.color || 'bg-blue-500'}
              `}>
                {element.icon || element.label?.charAt(0)}
              </div>
              <span className="text-xs mt-1 text-center">{element.label}</span>
              {index < (diagram.elements?.length - 1) && (
                <div className="text-2xl text-gray-400 mx-2">→</div>
              )}
            </div>
          ))}
        </div>
        
        {diagram.description && (
          <p className="text-sm text-gray-600 mt-4 text-center">
            {diagram.description}
          </p>
        )}
      </div>
    );
  } catch (error) {
    return <div className="text-red-500">Erro ao carregar diagrama visual</div>;
  }
};

// Componente para passo a passo
const StepByStep: React.FC<{ content: string; cognitiveProfile: string }> = ({ 
  content, 
  cognitiveProfile 
}) => {
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  
  try {
    const guide = JSON.parse(content);
    const steps = guide.steps || [];

    const toggleStep = (index: number) => {
      const newCompleted = new Set(completedSteps);
      if (newCompleted.has(index)) {
        newCompleted.delete(index);
      } else {
        newCompleted.add(index);
      }
      setCompletedSteps(newCompleted);
    };

    return (
      <div className="step-by-step">
        <h5 className="font-medium mb-4">{guide.title}</h5>
        
        <div className="space-y-3">
          {steps.map((step: any, index: number) => (
            <div 
              key={index}
              className={`
                border rounded-lg p-3 cursor-pointer transition-all
                ${completedSteps.has(index) 
                  ? 'bg-green-50 border-green-300' 
                  : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }
              `}
              onClick={() => toggleStep(index)}
            >
              <div className="flex items-start space-x-3">
                <div className={`
                  w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold
                  ${completedSteps.has(index) 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-300 text-gray-600'
                  }
                `}>
                  {completedSteps.has(index) ? '✓' : index + 1}
                </div>
                <div className="flex-1">
                  <h6 className="font-medium">{step.title}</h6>
                  <p className="text-sm text-gray-600 mt-1">{step.description}</p>
                  {step.tip && (
                    <div className="mt-2 text-xs text-blue-600 bg-blue-50 p-2 rounded">
                      💡 <strong>Dica:</strong> {step.tip}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          Progresso: {completedSteps.size} de {steps.length} passos concluídos
        </div>
      </div>
    );
  } catch (error) {
    return <div className="text-red-500">Erro ao carregar guia passo a passo</div>;
  }
};

// Componente para quiz interativo
const InteractiveQuiz: React.FC<{ content: string; cognitiveProfile: string }> = ({ 
  content, 
  cognitiveProfile 
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<{ [key: number]: number }>({});
  const [showResults, setShowResults] = useState(false);
  
  try {
    const quiz = JSON.parse(content);
    const questions = quiz.questions || [];

    const handleAnswer = (questionIndex: number, answerIndex: number) => {
      setSelectedAnswers(prev => ({
        ...prev,
        [questionIndex]: answerIndex
      }));
    };

    const calculateScore = () => {
      let correct = 0;
      questions.forEach((question: any, index: number) => {
        if (selectedAnswers[index] === question.correctAnswer) {
          correct++;
        }
      });
      return correct;
    };

    return (
      <div className="interactive-quiz">
        <h5 className="font-medium mb-4">{quiz.title}</h5>
        
        {!showResults ? (
          <div>
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">
                  Pergunta {currentQuestion + 1} de {questions.length}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-theme-accent h-2 rounded-full transition-all"
                  style={{ width: `${((currentQuestion + 1) / questions.length) * 100}%` }}
                />
              </div>
            </div>

            {questions[currentQuestion] && (
              <div className="question">
                <h6 className="font-medium mb-3">{questions[currentQuestion].question}</h6>
                
                <div className="space-y-2">
                  {questions[currentQuestion].options?.map((option: string, index: number) => (
                    <button
                      key={index}
                      onClick={() => handleAnswer(currentQuestion, index)}
                      className={`
                        w-full text-left p-3 rounded border transition-all
                        ${selectedAnswers[currentQuestion] === index
                          ? 'bg-blue-100 border-blue-300'
                          : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                        }
                      `}
                    >
                      {option}
                    </button>
                  ))}
                </div>

                <div className="flex justify-between mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentQuestion(Math.max(0, currentQuestion - 1))}
                    disabled={currentQuestion === 0}
                  >
                    Anterior
                  </Button>
                  
                  {currentQuestion === questions.length - 1 ? (
                    <Button
                      onClick={() => setShowResults(true)}
                      disabled={Object.keys(selectedAnswers).length !== questions.length}
                    >
                      Ver Resultados
                    </Button>
                  ) : (
                    <Button
                      onClick={() => setCurrentQuestion(currentQuestion + 1)}
                      disabled={selectedAnswers[currentQuestion] === undefined}
                    >
                      Próxima
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="results">
            <div className="text-center mb-4">
              <div className="text-3xl font-bold text-theme-accent">
                {calculateScore()}/{questions.length}
              </div>
              <p className="text-gray-600">
                Você acertou {Math.round((calculateScore() / questions.length) * 100)}% das perguntas!
              </p>
            </div>
            
            <Button
              variant="outline"
              onClick={() => {
                setShowResults(false);
                setCurrentQuestion(0);
                setSelectedAnswers({});
              }}
              className="w-full"
            >
              Tentar Novamente
            </Button>
          </div>
        )}
      </div>
    );
  } catch (error) {
    return <div className="text-red-500">Erro ao carregar quiz interativo</div>;
  }
};

// Componente para simulação
const Simulation: React.FC<{ content: string; cognitiveProfile: string }> = ({ 
  content, 
  cognitiveProfile 
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  
  try {
    const simulation = JSON.parse(content);
    
    const runSimulation = () => {
      setIsRunning(true);
      setCurrentStep(0);
      
      const interval = setInterval(() => {
        setCurrentStep(prev => {
          if (prev >= (simulation.steps?.length || 0) - 1) {
            setIsRunning(false);
            clearInterval(interval);
            return prev;
          }
          return prev + 1;
        });
      }, 1000);
    };

    return (
      <div className="simulation">
        <h5 className="font-medium mb-4">{simulation.title}</h5>
        
        <div className="bg-gray-100 p-4 rounded mb-4 min-h-32 flex items-center justify-center">
          {simulation.steps?.[currentStep] ? (
            <div className="text-center">
              <div className="text-2xl mb-2">{simulation.steps[currentStep].visual || '⚙️'}</div>
              <p className="font-medium">{simulation.steps[currentStep].description}</p>
            </div>
          ) : (
            <p className="text-gray-500">Clique em "Executar" para iniciar a simulação</p>
          )}
        </div>
        
        <div className="flex justify-center space-x-2">
          <Button
            onClick={runSimulation}
            disabled={isRunning}
          >
            {isRunning ? 'Executando...' : 'Executar Simulação'}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => {
              setIsRunning(false);
              setCurrentStep(0);
            }}
          >
            Reiniciar
          </Button>
        </div>
        
        {simulation.steps && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-theme-accent h-2 rounded-full transition-all"
                style={{ width: `${((currentStep + 1) / simulation.steps.length) * 100}%` }}
              />
            </div>
            <p className="text-xs text-gray-600 mt-1 text-center">
              Passo {currentStep + 1} de {simulation.steps.length}
            </p>
          </div>
        )}
      </div>
    );
  } catch (error) {
    return <div className="text-red-500">Erro ao carregar simulação</div>;
  }
};
