import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/Button';
import { logger } from '@/lib/logger';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Atualiza o estado para que o próximo render mostre a UI alternativa
    return { 
      hasError: true,
      error 
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Registra o erro no sistema de logging
    logger.error('Erro não tratado capturado por ErrorBoundary', error, {
      componentStack: errorInfo.componentStack
    });
  }
  
  handleResetError = (): void => {
    this.setState({
      hasError: false,
      error: null
    });
  }
  
  handleGoToHome = (): void => {
    window.location.href = '/';
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // Tela de fallback personalizada
      if (this.props.fallback) {
        return this.props.fallback;
      }
      
      // Tela de fallback padrão
      return (
        <div className="min-h-screen flex items-center justify-center bg-theme-secondary p-4">
          <div className="max-w-md w-full card-theme rounded-lg p-6">
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 rounded-full bg-theme-tertiary">
                <svg className="w-6 h-6 text-theme-secondary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-theme-primary">Oops! Algo deu errado</h2>
              <div className="text-theme-secondary mb-6">
                <p className="mb-4">
                  Encontramos um problema ao carregar esta página. Nossa equipe foi notificada.
                </p>
                {this.state.error && (
                  <div className="text-left p-3 bg-gray-100 rounded-md text-xs text-gray-800 font-mono mb-4 overflow-auto max-h-32">
                    {this.state.error.toString()}
                  </div>
                )}
              </div>
              <div className="flex flex-col sm:flex-row justify-center gap-3">
                <Button variant="outline" onClick={this.handleResetError}>
                  Tentar novamente
                </Button>
                <Button onClick={this.handleGoToHome}>
                  Voltar para a página inicial
                </Button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
