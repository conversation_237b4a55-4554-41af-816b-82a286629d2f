import React from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/Button';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-lg w-full text-center">
        <h1 className="text-6xl font-bold text-blue-600 mb-6">404</h1>
        <h2 className="text-3xl font-semibold text-gray-900 mb-4">Página não encontrada</h2>
        <p className="text-lg text-gray-600 mb-8">
          Desculpe, a página que você está procurando não existe ou foi movida.
        </p>
        <div className="flex justify-center">
          <Link to="/">
            <Button variant="primary">
              Voltar para a página inicial
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
