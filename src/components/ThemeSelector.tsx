import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { COGNITIVE_PROFILES } from '@/constants';
import { Card } from '@/components/ui/Card';

interface ThemeSelectorProps {
  className?: string;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({ className = '' }) => {
  const { theme, persona, toggleTheme, setPersona } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  const currentProfile = COGNITIVE_PROFILES.find(p => p.id === persona);

  return (
    <div className={`relative ${className}`}>
      {/* Botão para abrir seletor */}
      <div className="flex items-center space-x-2">
        {/* Toggle de tema claro/escuro */}
        <button
          onClick={toggleTheme}
          className="p-2 rounded-full bg-theme-secondary hover:bg-theme-tertiary transition-colors focus:outline-none focus-visible"
          aria-label={theme === 'dark' ? "Ativar modo claro" : "Ativar modo escuro"}
        >
          {theme === 'dark' ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-theme-primary" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm-.707 9.193a1 1 0 010-1.414l.707-.707a1 1 0 111.414 1.414l-.707.707a1 1 0 01-1.414 0zm12.728 0l-.707-.707a1 1 0 10-1.414 1.414l.707.707a1 1 0 001.414-1.414zM4 11a1 1 0 110-2H3a1 1 0 110-2h1zm10.465-4.536a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-theme-primary" viewBox="0 0 20 20" fill="currentColor">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          )}
        </button>

        {/* Seletor de persona */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-theme-secondary hover:bg-theme-tertiary transition-colors focus:outline-none focus-visible"
          aria-label="Selecionar persona cognitiva"
        >
          <span className="text-lg">{currentProfile?.icon}</span>
          <span className="text-sm font-medium text-theme-primary hidden sm:block">
            {currentProfile?.name}
          </span>
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-4 w-4 text-theme-secondary transition-transform ${isOpen ? 'rotate-180' : ''}`}
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* Dropdown de personas */}
      {isOpen && (
        <>
          {/* Overlay para fechar */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Menu dropdown */}
          <div className="absolute right-0 mt-2 w-80 z-20">
            <Card className="p-0">
              <div className="p-4 border-b border-theme">
                <h3 className="text-lg font-semibold text-theme-primary">
                  Perfil Cognitivo
                </h3>
                <p className="text-sm text-theme-secondary mt-1">
                  Escolha o perfil que melhor se adapta às suas necessidades
                </p>
              </div>
              
              <div className="p-2 space-y-1 max-h-64 overflow-y-auto custom-scrollbar">
                {COGNITIVE_PROFILES.map((profile) => (
                  <button
                    key={profile.id}
                    onClick={() => {
                      setPersona(profile.id);
                      setIsOpen(false);
                    }}
                    className={`w-full text-left p-3 rounded-lg transition-colors hover:bg-theme-secondary focus:outline-none focus-visible ${
                      persona === profile.id ? 'bg-theme-tertiary' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-xl flex-shrink-0">{profile.icon}</span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h4 className="text-sm font-medium text-theme-primary">
                            {profile.name}
                          </h4>
                          {persona === profile.id && (
                            <div className="h-2 w-2 rounded-full bg-green-500 flex-shrink-0" />
                          )}
                        </div>
                        <p className="text-xs text-theme-secondary mt-1 line-clamp-2">
                          {profile.description}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="p-4 border-t border-theme bg-theme-secondary">
                <p className="text-xs text-theme-secondary text-center">
                  As configurações são salvas automaticamente
                </p>
              </div>
            </Card>
          </div>
        </>
      )}
    </div>
  );
};

export default ThemeSelector;