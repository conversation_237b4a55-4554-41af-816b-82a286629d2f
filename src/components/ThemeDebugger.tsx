import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { CognitiveType } from '@/types/cognitive';

export const ThemeDebugger: React.FC = () => {
  const { theme, persona, setTheme, setPersona, toggleTheme } = useTheme();

  return (
    <Card className="card-theme p-6 m-4 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4 text-theme-primary">🎨 Theme Debugger</h2>
      
      <div className="space-y-4">
        {/* Status atual */}
        <div className="bg-theme-secondary p-4 rounded-lg">
          <h3 className="font-semibold text-theme-primary mb-2">Status Atual:</h3>
          <p className="text-theme-secondary">Tema: <span className="font-mono bg-theme-tertiary px-2 py-1 rounded">{theme}</span></p>
          <p className="text-theme-secondary">Persona: <span className="font-mono bg-theme-tertiary px-2 py-1 rounded">{persona}</span></p>
        </div>

        {/* Controles de tema */}
        <div className="space-y-2">
          <h3 className="font-semibold text-theme-primary">Controles de Tema:</h3>
          <div className="flex gap-2 flex-wrap">
            <Button variant="primary" onClick={toggleTheme}>
              Alternar Tema ({theme === 'light' ? 'Escuro' : 'Claro'})
            </Button>
            <Button variant="secondary" onClick={() => setTheme('light')}>
              Forçar Claro
            </Button>
            <Button variant="secondary" onClick={() => setTheme('dark')}>
              Forçar Escuro
            </Button>
          </div>
        </div>

        {/* Controles de persona */}
        <div className="space-y-2">
          <h3 className="font-semibold text-theme-primary">Controles de Persona:</h3>
          <div className="flex gap-2 flex-wrap">
            <Button 
              variant={persona === CognitiveType.DEFAULT ? "primary" : "secondary"} 
              onClick={() => setPersona(CognitiveType.DEFAULT)}
            >
              DEFAULT
            </Button>
            <Button 
              variant={persona === CognitiveType.ADHD ? "primary" : "secondary"} 
              onClick={() => setPersona(CognitiveType.ADHD)}
            >
              ADHD
            </Button>
            <Button 
              variant={persona === CognitiveType.AUTISM ? "primary" : "secondary"} 
              onClick={() => setPersona(CognitiveType.AUTISM)}
            >
              AUTISM
            </Button>
            <Button 
              variant={persona === CognitiveType.DYSLEXIA ? "primary" : "secondary"} 
              onClick={() => setPersona(CognitiveType.DYSLEXIA)}
            >
              DYSLEXIA
            </Button>
          </div>
        </div>

        {/* Teste de cores */}
        <div className="space-y-2">
          <h3 className="font-semibold text-theme-primary">Teste de Cores:</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <div className="bg-theme-primary text-theme-inverse p-2 rounded text-center text-sm">
              Primary BG
            </div>
            <div className="bg-theme-secondary text-theme-primary p-2 rounded text-center text-sm">
              Secondary BG
            </div>
            <div className="bg-theme-tertiary text-theme-primary p-2 rounded text-center text-sm">
              Tertiary BG
            </div>
            <div className="bg-theme-quaternary text-theme-primary p-2 rounded text-center text-sm">
              Quaternary BG
            </div>
          </div>
        </div>

        {/* Teste de botões */}
        <div className="space-y-2">
          <h3 className="font-semibold text-theme-primary">Teste de Botões:</h3>
          <div className="flex gap-2 flex-wrap">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="accent">Accent</Button>
            <Button variant="success">Success</Button>
            <Button variant="warning">Warning</Button>
            <Button variant="error">Error</Button>
          </div>
        </div>

        {/* Teste de texto */}
        <div className="space-y-2">
          <h3 className="font-semibold text-theme-primary">Teste de Texto:</h3>
          <div className="space-y-1">
            <p className="text-theme-primary">Texto primário</p>
            <p className="text-theme-secondary">Texto secundário</p>
            <p className="text-theme-tertiary">Texto terciário</p>
            <p className="text-theme-muted">Texto esmaecido</p>
          </div>
        </div>

        {/* CSS Variables atuais */}
        <div className="space-y-2">
          <h3 className="font-semibold text-theme-primary">CSS Variables (Debug):</h3>
          <div className="bg-theme-tertiary p-3 rounded text-xs font-mono text-theme-secondary overflow-x-auto">
            <div>--background-primary: rgb(var(--background-primary))</div>
            <div>--text-primary: rgb(var(--text-primary))</div>
            <div>--color-primary: rgb(var(--color-primary))</div>
            <div>--border-color: rgb(var(--border-color))</div>
          </div>
        </div>

        {/* Instruções */}
        <div className="bg-theme-secondary p-4 rounded-lg border-l-4 border-theme-focus">
          <h4 className="font-semibold text-theme-primary mb-2">💡 Como usar:</h4>
          <ul className="text-theme-secondary text-sm space-y-1">
            <li>• Teste diferentes combinações de tema e persona</li>
            <li>• Verifique se as cores mudam corretamente</li>
            <li>• Observe se os botões e textos ficam legíveis</li>
            <li>• Use este componente para debug durante desenvolvimento</li>
          </ul>
        </div>
      </div>
    </Card>
  );
};
