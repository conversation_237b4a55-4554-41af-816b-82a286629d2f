import React, { useState } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useTheme } from '@/contexts/ThemeContext';
import { useFlow } from '@/components/FlowProvider';
import { ThemeToggle } from '@/components/ThemeToggle';
import { PersonaPreview } from '@/components/PersonaPreview';
import { FlowConfigModal } from '@/components/FlowConfigModal';
import { Button } from '@/components/ui/Button';
import { COGNITIVE_PROFILES } from '@/constants';

export const LoggedHeader: React.FC = () => {
  const { user, signOut } = useAuth();
  const { persona } = useTheme();
  const { setShowConfigModal, showConfigModal } = useFlow();
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Buscar o nome traduzido da persona
  const currentProfile = COGNITIVE_PROFILES.find(p => p.id === persona);
  const personaName = currentProfile?.name || 'Padrão';

  const handleConfigSuccess = () => {
    // Callback para quando a configuração for salva com sucesso
    console.log('Configuração do Flow salva com sucesso');
  };

  return (
    <>
      {/* Header fixo com design igual ao da home */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-theme-secondary border-b border-theme shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          {/* Logo e indicador de persona */}
          <div className="flex items-center space-x-3">
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center shadow-sm"
              style={{
                background: `linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-accent)))`
              }}
            >
              <span className="text-white font-bold text-sm">🧠</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-theme-primary">
                Flow AdaptEd
              </h1>
              <div className="flex items-center space-x-2 text-xs text-theme-secondary">
                <span>Perfil:</span>
                <span className="font-medium text-theme-primary">
                  {personaName}
                </span>
              </div>
            </div>
          </div>

          {/* Controles do lado direito */}
          <div className="flex items-center space-x-3">
            {/* Seletor de persona compacto */}
            <PersonaPreview compact />
            
            {/* Toggle de tema */}
            <ThemeToggle />
            
            {/* Botão de configurações do Flow */}
            <Button
              variant="ghost"
              onClick={() => setShowConfigModal(true)}
              className="p-2 rounded-lg bg-theme-secondary/50 border border-theme hover:bg-theme-tertiary transition-all duration-200"
              aria-label="Configurações do Flow"
            >
              <div className="flex items-center space-x-2">
                <svg className="w-5 h-5 text-theme-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span className="text-sm font-medium text-theme-primary hidden sm:block">
                  Flow
                </span>
              </div>
            </Button>
            
            {/* Menu do usuário */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="relative p-2 rounded-lg bg-theme-secondary/50 border border-theme hover:bg-theme-tertiary transition-all duration-200 hover:scale-105"
                aria-label="Menu do usuário"
              >
                {user?.photoURL ? (
                  <img
                    src={user.photoURL}
                    alt={user.displayName || 'Usuário'}
                    className="w-6 h-6 rounded-full"
                  />
                ) : (
                  <div className="w-6 h-6 rounded-full bg-theme-accent flex items-center justify-center">
                    <span className="text-white text-xs font-bold">
                      {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                    </span>
                  </div>
                )}

                {/* Indicador sutil de dropdown */}
                <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-theme-accent rounded-full border border-theme-primary"></div>
              </button>

              {/* Dropdown do usuário */}
              {showUserMenu && (
                <>
                  <div 
                    className="fixed inset-0 z-40" 
                    onClick={() => setShowUserMenu(false)}
                  />
                  
                  <div className="absolute top-full right-0 mt-2 w-64 bg-theme-primary border border-theme rounded-lg shadow-xl z-50 overflow-hidden">
                    {/* Info do usuário */}
                    <div className="p-4 border-b border-theme bg-theme-secondary">
                      <div className="flex items-center space-x-3">
                        {user?.photoURL ? (
                          <img
                            src={user.photoURL}
                            alt={user.displayName || 'Usuário'}
                            className="w-12 h-12 rounded-full border-2 border-theme-accent"
                          />
                        ) : (
                          <div className="w-12 h-12 rounded-full bg-theme-accent flex items-center justify-center border-2 border-theme-accent">
                            <span className="text-white font-bold text-lg">
                              {user?.displayName?.charAt(0) || user?.email?.charAt(0) || 'U'}
                            </span>
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-theme-primary truncate text-base">
                            {user?.displayName || 'Usuário'}
                          </p>
                          <p className="text-sm text-theme-secondary truncate">
                            {user?.email}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Opções do menu */}
                    <div className="p-2">
                      <button
                        onClick={() => {
                          signOut();
                          setShowUserMenu(false);
                        }}
                        className="w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors duration-150 hover:bg-theme-secondary text-theme-secondary hover:text-theme-primary"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span className="font-medium text-sm">Sair</span>
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Modal de configuração do Flow */}
      <FlowConfigModal
        isOpen={showConfigModal}
        onClose={() => setShowConfigModal(false)}
        onSuccess={handleConfigSuccess}
      />
    </>
  );
};
