/**
 * Sistema de analytics e monitoramento de performance
 */
import { logger } from '@/lib/logger';

export interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  [key: string]: unknown;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  rating?: 'good' | 'needs-improvement' | 'poor';
  delta?: number;
  id?: string;
}

class Analytics {
  private initialized = false;
  private userId: string | null = null;
  private sessionId: string | null = null;
  private gaId: string | null = null;
  
  /**
   * Inicializa o sistema de analytics
   * @param gaId ID do Google Analytics (opcional)
   */
  initialize(gaId?: string): void {
    if (this.initialized) return;
    
    this.gaId = gaId || null;
    
    // Inicializa Google Analytics se ID for fornecido
    if (this.gaId && typeof window !== 'undefined') {
      this.setupGoogleAnalytics(this.gaId);
    }
    
    // Configura monitoramento de performance
    this.setupPerformanceMonitoring();
    
    this.initialized = true;
    logger.info('Sistema de analytics inicializado', { 
      useGoogleAnalytics: !!this.gaId 
    });
  }
  
  /**
   * Define o ID do usuário para rastreamento
   */
  setUserId(userId: string | null): void {
    this.userId = userId;
    
    // Configura GA se disponível
    if (userId && this.gaId && window.gtag) {
      window.gtag('set', { user_id: userId });
    }
  }
  
  /**
   * Define o ID da sessão para rastreamento
   */
  setSessionId(sessionId: string | null): void {
    this.sessionId = sessionId;
  }
  
  /**
   * Registra um evento de analytics
   */
  trackEvent(event: AnalyticsEvent): void {
    if (!this.initialized) {
      logger.warn('Analytics não inicializado ao tentar registrar evento', { event });
      return;
    }
    
    // Registra no logger
    logger.info(`Evento: ${event.category} / ${event.action}`, {
      eventType: 'analytics',
      ...event
    });
    
    // Envia para GA se disponível
    if (this.gaId && window.gtag) {
      window.gtag('event', event.action, {
        'event_category': event.category,
        'event_label': event.label,
        'value': event.value,
        ...(this.userId ? { 'user_id': this.userId } : {}),
        ...(this.sessionId ? { 'session_id': this.sessionId } : {})
      });
    }
  }
  
  /**
   * Rastreia uma visualização de página
   */
  trackPageView(path: string, title?: string): void {
    if (!this.initialized) {
      logger.warn('Analytics não inicializado ao tentar registrar visualização de página', { path });
      return;
    }
    
    // Registra no logger
    logger.info(`Visualização de página: ${path}`, {
      eventType: 'pageview',
      path,
      title
    });
    
    // Envia para GA se disponível
    if (this.gaId && window.gtag) {
      window.gtag('event', 'page_view', {
        page_path: path,
        page_title: title,
        ...(this.userId ? { 'user_id': this.userId } : {}),
        ...(this.sessionId ? { 'session_id': this.sessionId } : {})
      });
    }
  }
  
  /**
   * Rastreia uma métrica de performance
   */
  trackPerformance(metric: PerformanceMetric): void {
    logger.info(`Performance métrica: ${metric.name} = ${metric.value}`, {
      eventType: 'performance',
      ...metric
    });
    
    // Envia para GA se disponível
    if (this.gaId && window.gtag) {
      window.gtag('event', 'web_vitals', {
        eventCategory: 'Web Vitals',
        eventAction: metric.name,
        eventValue: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        eventLabel: metric.rating,
        nonInteraction: true,
      });
    }
  }
  
  /**
   * Rastreia um erro na aplicação
   */
  trackError(error: Error, context?: Record<string, unknown>): void {
    logger.error('Erro de aplicação', error, {
      eventType: 'error',
      ...context
    });
    
    // Envia para GA se disponível
    if (this.gaId && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: false,
        ...context
      });
    }
  }
  
  /**
   * Configura o Google Analytics
   */
  private setupGoogleAnalytics(gaId: string): void {
    // Adiciona script do GA
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    document.head.appendChild(script);
    
    // Inicializa GA
    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };
    
    window.gtag('js', new Date());
    window.gtag('config', gaId, {
      anonymize_ip: true,
      send_page_view: false
    });
  }
  
  /**
   * Configura monitoramento de métricas de performance
   */
  private setupPerformanceMonitoring(): void {
    // Importa Web Vitals dinâmicamente para não afetar o carregamento inicial
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ onCLS, onFID, onLCP, onTTFB, onINP }) => {
        // Core Web Vitals
        onCLS(metric => this.trackPerformance({
          name: 'CLS',
          value: metric.value,
          rating: metric.rating
        }));
        
        onLCP(metric => this.trackPerformance({
          name: 'LCP',
          value: metric.value,
          rating: metric.rating
        }));
        
        onFID(metric => this.trackPerformance({
          name: 'FID',
          value: metric.value,
          rating: metric.rating
        }));
        
        // Métricas adicionais
        onTTFB(metric => this.trackPerformance({
          name: 'TTFB',
          value: metric.value,
          rating: metric.rating
        }));
        
        onINP(metric => this.trackPerformance({
          name: 'INP',
          value: metric.value,
          rating: metric.rating
        }));
      }).catch(error => {
        logger.error('Erro ao carregar web-vitals', error);
      });
    }
  }
}

// Adiciona tipos para o Google Analytics
declare global {
  interface Window {
    dataLayer: any[];
    gtag: (...args: any[]) => void;
  }
}

// Exporta instância singleton
export const analytics = new Analytics();

export default analytics;
