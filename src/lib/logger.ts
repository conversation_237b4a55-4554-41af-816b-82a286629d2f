/**
 * Sistema de logging centralizado para a aplicação
 */

// Níveis de log disponíveis
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Configuração do logger
export interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  remoteUrl?: string;
  appVersion?: string;
  environment?: string;
}

// Dados adicionais para o log
export interface LogContext {
  [key: string]: unknown;
}

// Opções para inicialização do logger
const DEFAULT_CONFIG: LoggerConfig = {
  minLevel: 'info',
  enableConsole: true,
  enableRemote: false,
  environment: import.meta.env.MODE || 'development',
  appVersion: import.meta.env.VITE_APP_VERSION || '0.1.0',
};

// Prioridade dos níveis de log para filtro
const LOG_LEVELS: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
};

class Logger {
  private config: LoggerConfig = DEFAULT_CONFIG;
  private userId: string | null = null;
  private sessionId: string | null = null;

  /**
   * Configura o logger
   */
  configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Define o ID do usuário para rastreamento
   */
  setUserId(userId: string | null): void {
    this.userId = userId;
  }

  /**
   * Define o ID da sessão para rastreamento
   */
  setSessionId(sessionId: string | null): void {
    this.sessionId = sessionId;
  }

  /**
   * Log de depuração - detalhes técnicos úteis para desenvolvimento
   */
  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  /**
   * Log informativo - acontecimentos normais do sistema
   */
  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  /**
   * Log de aviso - situações que podem gerar problemas
   */
  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  /**
   * Log de erro - falhas e exceções
   */
  error(message: string, error?: Error, context?: LogContext): void {
    const errorContext = error
      ? {
          errorMessage: error.message,
          stack: error.stack,
          name: error.name,
          ...context,
        }
      : context;

    this.log('error', message, errorContext);
  }

  /**
   * Método principal de logging
   */
  private log(level: LogLevel, message: string, context?: LogContext): void {
    // Verifica se o nível de log está habilitado
    if (LOG_LEVELS[level] < LOG_LEVELS[this.config.minLevel]) {
      return;
    }

    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      userId: this.userId,
      sessionId: this.sessionId,
      environment: this.config.environment,
      appVersion: this.config.appVersion,
      ...(context || {}),
    };

    // Log no console
    if (this.config.enableConsole) {
      this.logToConsole(level, message, logEntry);
    }

    // Log remoto
    if (this.config.enableRemote && this.config.remoteUrl) {
      this.logToRemote(logEntry);
    }
  }

  /**
   * Exibe log no console com formatação colorida
   */
  private logToConsole(level: LogLevel, message: string, data: Record<string, unknown>): void {
    const styles: Record<LogLevel, string> = {
      debug: 'color: #6c757d',
      info: 'color: #0d6efd',
      warn: 'color: #ffc107',
      error: 'color: #dc3545; font-weight: bold',
    };

    const prefix = `[${data.timestamp}] [${level.toUpperCase()}]`;

    // Exibir mensagem com estilo
    console[level as keyof Console](`%c${prefix} ${message}`, styles[level]);

    // Exibir dados adicionais se houver
    const contextData = { ...data };
    delete contextData.timestamp;
    delete contextData.level;
    delete contextData.message;

    if (Object.keys(contextData).length > 0) {
      console.groupCollapsed('Detalhes adicionais');
      console.dir(contextData);
      console.groupEnd();
    }
  }

  /**
   * Envia log para serviço remoto
   */
  private logToRemote(data: Record<string, unknown>): void {
    if (!this.config.remoteUrl) return;

    // Usa sendBeacon para não bloquear a UI
    if (navigator.sendBeacon) {
      const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
      navigator.sendBeacon(this.config.remoteUrl, blob);
    } else {
      // Fallback para fetch
      fetch(this.config.remoteUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
        // Não esperar resposta para não bloquear
        keepalive: true,
      }).catch(() => {
        // Erros de rede são silenciados para evitar loops
      });
    }
  }
}

// Exporta instância singleton do logger
export const logger = new Logger();

// Hook para inicializar o logger com o usuário atual
export function initLogger(userId?: string, sessionId?: string): void {
  // Configura logger com informações do ambiente
  logger.configure({
    minLevel: import.meta.env.DEV ? 'debug' : 'info',
    enableConsole: true,
    enableRemote: import.meta.env.PROD,
    remoteUrl: import.meta.env.VITE_LOG_ENDPOINT,
  });

  // Define informações de usuário e sessão, se fornecidos
  if (userId) {
    logger.setUserId(userId);
  }

  if (sessionId) {
    logger.setSessionId(sessionId);
  }
}

export default logger;
