import { z } from 'zod';

/**
 * Define e valida as variáveis de ambiente requeridas pela aplicação.
 * Lan<PERSON> erro se as variáveis obrigatórias não estiverem definidas.
 */
const envSchema = z.object({
  // Firebase
  VITE_FIREBASE_API_KEY: z.string().min(1, 'API Key do Firebase é obrigatória'),
  VITE_FIREBASE_AUTH_DOMAIN: z.string().min(1, 'Auth Domain do Firebase é obrigatório'),
  VITE_FIREBASE_PROJECT_ID: z.string().min(1, 'Project ID do Firebase é obrigatório'),
  VITE_FIREBASE_STORAGE_BUCKET: z.string().min(1, 'Storage Bucket do Firebase é obrigatório'),
  VITE_FIREBASE_MESSAGING_SENDER_ID: z.string().min(1, 'Messaging Sender ID do Firebase é obrigatório'),
  VITE_FIREBASE_APP_ID: z.string().min(1, 'App ID do Firebase é obrigatório'),

  // Flow (opcional - pode ser configurado via UI)
  VITE_FLOW_BASE_URL: z.string().url().default('https://flow.ciandt.com'),
  VITE_FLOW_TENANT: z.string().default('cit'),
  VITE_FLOW_CLIENT_ID: z.string().optional(),
  VITE_FLOW_CLIENT_SECRET: z.string().optional(),
  VITE_FLOW_APP_TO_ACCESS: z.string().default('llm-api'),
});

// Função para tentar obter variáveis de ambiente, com valores padrão para desenvolvimento
function getEnvWithDefaults() {
  const processEnv = {
    ...import.meta.env,
  };
  
  try {
    return envSchema.parse(processEnv);
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('Erro de configuração de ambiente:', error.format());
      console.error('Valores recebidos:', {
        VITE_FIREBASE_API_KEY: processEnv.VITE_FIREBASE_API_KEY ? '***definido***' : 'undefined',
        VITE_FIREBASE_AUTH_DOMAIN: processEnv.VITE_FIREBASE_AUTH_DOMAIN,
        VITE_FIREBASE_PROJECT_ID: processEnv.VITE_FIREBASE_PROJECT_ID,
        VITE_FLOW_CLIENT_ID: processEnv.VITE_FLOW_CLIENT_ID ? '***definido***' : 'undefined'
      });
      throw new Error(
        `Variáveis de ambiente inválidas ou ausentes. Verifique o console para mais detalhes.`
      );
    }
    throw error;
  }
}

// Exporta as variáveis de ambiente validadas
export const env = getEnvWithDefaults();

// Exporta tipos para uso na aplicação
export type Env = z.infer<typeof envSchema>;
