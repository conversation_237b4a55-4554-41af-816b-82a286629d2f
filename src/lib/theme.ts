/**
 * Utilit<PERSON><PERSON>s para o sistema de temas
 */

import { 
  ThemeMode, 
  CognitivePersona, 
  ThemeColors, 
  ThemeConfig,
  PersonaThemeConfig,
  ThemeBackgroundClass,
  ThemeTextClass,
  ThemeBorderClass,
  ThemeButtonClass,
  ThemeCardClass,
  ThemeInputClass
} from '@/types/theme';

// Configurações das personas
export const PERSONA_CONFIGS: PersonaThemeConfig[] = [
  {
    id: 'DEFAULT',
    name: '<PERSON><PERSON><PERSON>',
    description: 'Configurações padrão do sistema',
    icon: '⚙️',
    colorScheme: {
      light: {},
      dark: {}
    },
    uiPreferences: {
      fontSize: 'base',
      borderRadius: 'md',
      animationLevel: 'normal'
    }
  },
  {
    id: 'ADHD',
    name: 'TDAH Foco',
    description: 'Otimizado para atenção e engajamento',
    icon: '🎯',
    colorScheme: {
      light: {
        primary: '59, 130, 246',
        accent: '16, 185, 129',
        backgroundPrimary: '248, 250, 252'
      },
      dark: {
        primary: '96, 165, 250',
        accent: '52, 211, 153',
        backgroundPrimary: '15, 23, 42'
      }
    },
    uiPreferences: {
      fontSize: 'lg',
      borderRadius: 'lg',
      animationLevel: 'reduced',
      buttonMinSize: 44,
      maxElementsPerScreen: 3
    }
  },
  {
    id: 'AUTISM',
    name: 'Autismo Estruturado',
    description: 'Interface clara, previsível e calma',
    icon: '🧩',
    colorScheme: {
      light: {
        primary: '100, 116, 139',
        accent: '139, 92, 246',
        backgroundPrimary: '248, 250, 252'
      },
      dark: {
        primary: '148, 163, 184',
        accent: '196, 181, 253',
        backgroundPrimary: '15, 23, 42'
      }
    },
    uiPreferences: {
      fontSize: 'base',
      borderRadius: 'sm',
      animationLevel: 'none',
      fontFamily: 'system-ui'
    }
  },
  {
    id: 'DYSLEXIA',
    name: 'Dislexia Legível',
    description: 'Legibilidade aprimorada e suporte auditivo',
    icon: '📖',
    colorScheme: {
      light: {
        primary: '34, 197, 94',
        accent: '245, 158, 11',
        backgroundPrimary: '255, 254, 247',
        textPrimary: '23, 23, 23'
      },
      dark: {
        primary: '74, 222, 128',
        accent: '251, 191, 36',
        backgroundPrimary: '41, 37, 36',
        textPrimary: '250, 250, 249'
      }
    },
    uiPreferences: {
      fontSize: 'lg',
      fontFamily: 'OpenDyslexic',
      lineHeight: 1.8,
      letterSpacing: 0.05,
      wordSpacing: 0.1,
      borderRadius: 'md'
    }
  }
];

/**
 * Obtém a configuração de uma persona específica
 */
export function getPersonaConfig(persona: CognitivePersona): PersonaThemeConfig {
  return PERSONA_CONFIGS.find(config => config.id === persona) || PERSONA_CONFIGS[0];
}

/**
 * Gera classes CSS baseadas no tipo e variante
 */
export function getThemeClass(
  type: 'background' | 'text' | 'border' | 'button' | 'card' | 'input',
  variant: string = 'primary'
): string {
  const classMap = {
    background: {
      primary: 'bg-theme-primary',
      secondary: 'bg-theme-secondary',
      tertiary: 'bg-theme-tertiary',
      quaternary: 'bg-theme-quaternary'
    } as Record<string, ThemeBackgroundClass>,
    text: {
      primary: 'text-theme-primary',
      secondary: 'text-theme-secondary',
      tertiary: 'text-theme-tertiary',
      muted: 'text-theme-muted',
      inverse: 'text-theme-inverse'
    } as Record<string, ThemeTextClass>,
    border: {
      default: 'border-theme',
      light: 'border-theme-light',
      dark: 'border-theme-dark',
      focus: 'border-theme-focus'
    } as Record<string, ThemeBorderClass>,
    button: {
      primary: 'btn-primary',
      secondary: 'btn-secondary',
      accent: 'btn-accent',
      success: 'btn-success',
      warning: 'btn-warning',
      error: 'btn-error'
    } as Record<string, ThemeButtonClass>,
    card: {
      default: 'card-theme',
      elevated: 'card-theme-elevated'
    } as Record<string, ThemeCardClass>,
    input: {
      default: 'input-theme',
      success: 'input-success',
      warning: 'input-warning',
      error: 'input-error'
    } as Record<string, ThemeInputClass>
  };

  return classMap[type]?.[variant] || classMap[type]?.['default'] || '';
}

/**
 * Obtém o valor de uma variável CSS
 */
export function getCSSVariable(variable: string): string {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(`--${variable}`)
      .trim();
  }
  return '';
}

/**
 * Aplica estilos específicos da persona ao documento
 */
export function applyPersonaStyles(persona: CognitivePersona, theme: ThemeMode = 'light'): void {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;
  const config = getPersonaConfig(persona);

  // Aplicar atributos de dados
  root.setAttribute('data-persona', persona);
  root.setAttribute('data-theme', theme);

  // Aplicar preferências de UI específicas
  if (config.uiPreferences.fontFamily) {
    root.style.setProperty('--font-family-override', config.uiPreferences.fontFamily);
  }

  if (config.uiPreferences.fontSize) {
    root.style.setProperty('--font-size-override', config.uiPreferences.fontSize);
  }

  if (config.uiPreferences.lineHeight) {
    root.style.setProperty('--line-height-override', config.uiPreferences.lineHeight.toString());
  }

  if (config.uiPreferences.letterSpacing) {
    root.style.setProperty('--letter-spacing-override', `${config.uiPreferences.letterSpacing}em`);
  }

  if (config.uiPreferences.wordSpacing) {
    root.style.setProperty('--word-spacing-override', `${config.uiPreferences.wordSpacing}em`);
  }
}

/**
 * Valida o contraste entre duas cores
 */
export function validateThemeContrast(_foreground: string, _background: string): boolean {
  // Implementação básica - pode ser expandida com cálculos de contraste WCAG
  // Por enquanto, retorna true como placeholder
  return true;
}

/**
 * Obtém a configuração completa do tema atual
 */
export function getCurrentThemeConfig(): ThemeConfig | null {
  if (typeof document === 'undefined') return null;

  const root = document.documentElement;
  const theme = root.getAttribute('data-theme') as ThemeMode || 'light';
  const persona = root.getAttribute('data-persona') as CognitivePersona || 'DEFAULT';

  const colors: ThemeColors = {
    primary: getCSSVariable('color-primary'),
    primaryHover: getCSSVariable('color-primary-hover'),
    primaryLight: getCSSVariable('color-primary-light'),
    primaryDark: getCSSVariable('color-primary-dark'),
    secondary: getCSSVariable('color-secondary'),
    secondaryHover: getCSSVariable('color-secondary-hover'),
    secondaryLight: getCSSVariable('color-secondary-light'),
    accent: getCSSVariable('color-accent'),
    accentHover: getCSSVariable('color-accent-hover'),
    accentLight: getCSSVariable('color-accent-light'),
    success: getCSSVariable('color-success'),
    warning: getCSSVariable('color-warning'),
    error: getCSSVariable('color-error'),
    info: getCSSVariable('color-info'),
    backgroundPrimary: getCSSVariable('background-primary'),
    backgroundSecondary: getCSSVariable('background-secondary'),
    backgroundTertiary: getCSSVariable('background-tertiary'),
    backgroundQuaternary: getCSSVariable('background-quaternary'),
    textPrimary: getCSSVariable('text-primary'),
    textSecondary: getCSSVariable('text-secondary'),
    textTertiary: getCSSVariable('text-tertiary'),
    textMuted: getCSSVariable('text-muted'),
    textInverse: getCSSVariable('text-inverse'),
    borderColor: getCSSVariable('border-color'),
    borderColorLight: getCSSVariable('border-color-light'),
    borderColorDark: getCSSVariable('border-color-dark'),
    borderFocus: getCSSVariable('border-focus')
  };

  const shadows = {
    sm: getCSSVariable('shadow-sm'),
    md: getCSSVariable('shadow-md'),
    lg: getCSSVariable('shadow-lg'),
    xl: getCSSVariable('shadow-xl')
  };

  return {
    mode: theme,
    persona,
    colors,
    shadows
  };
}
