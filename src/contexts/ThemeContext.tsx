import React, { createContext, useContext, useEffect, useState } from 'react';
import { CognitiveType } from '@/types/cognitive';
import { ThemeMode, CognitivePersona, ThemeConfig } from '@/types/theme';
import { applyPersonaStyles, getCurrentThemeConfig } from '@/lib/theme';
import { userPreferencesService } from '@/services/userPreferencesService';

interface ThemeContextType {
  theme: ThemeMode;
  effectiveTheme: 'light' | 'dark'; // O tema realmente aplicado
  persona: CognitiveType;
  setTheme: (theme: ThemeMode) => void;
  setPersona: (persona: CognitiveType) => void;
  toggleTheme: () => void;
  getThemeConfig: () => ThemeConfig | null;
  syncWithFirestore: (userId: string) => Promise<void>;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  const [theme, setThemeState] = useState<ThemeMode>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') as ThemeMode;
      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        return savedTheme;
      }
    }
    return 'system'; // Padrão para seguir o sistema
  });

  // Calcular o tema efetivo baseado na preferência
  const [effectiveTheme, setEffectiveTheme] = useState<'light' | 'dark'>(() => {
    if (typeof window !== 'undefined') {
      if (theme === 'system') {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      }
      return theme === 'dark' ? 'dark' : 'light';
    }
    return 'light';
  });

  const [persona, setPersonaState] = useState<CognitiveType>(() => {
    if (typeof window !== 'undefined') {
      const savedPersona = localStorage.getItem('persona') as CognitiveType;
      if (savedPersona && Object.values(CognitiveType).includes(savedPersona)) {
        return savedPersona;
      }
    }
    return CognitiveType.DEFAULT;
  });

  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  const setPersona = (newPersona: CognitiveType) => {
    setPersonaState(newPersona);
    localStorage.setItem('persona', newPersona);
  };

  const toggleTheme = () => {
    const nextTheme = theme === 'light' ? 'dark' : theme === 'dark' ? 'system' : 'light';
    setTheme(nextTheme);
  };

  const getThemeConfig = (): ThemeConfig | null => {
    return getCurrentThemeConfig();
  };

  // Atualizar tema efetivo quando o tema ou preferência do sistema mudar
  useEffect(() => {
    const updateEffectiveTheme = () => {
      if (theme === 'system') {
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        setEffectiveTheme(systemPrefersDark ? 'dark' : 'light');
      } else {
        setEffectiveTheme(theme === 'dark' ? 'dark' : 'light');
      }
    };

    updateEffectiveTheme();

    // Escutar mudanças na preferência do sistema
    if (theme === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', updateEffectiveTheme);
      return () => mediaQuery.removeEventListener('change', updateEffectiveTheme);
    }
  }, [theme]);

  useEffect(() => {
    // Aplicar estilos da persona e tema usando o novo sistema
    applyPersonaStyles(persona as CognitivePersona, effectiveTheme);
  }, [effectiveTheme, persona]);

  // Remover o useEffect antigo pois agora é tratado no useEffect acima

  const value: ThemeContextType = {
    theme,
    effectiveTheme,
    persona,
    setTheme,
    setPersona,
    toggleTheme,
    getThemeConfig,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme deve ser usado dentro de um ThemeProvider');
  }
  return context;
};