import React from 'react';

const DebugApp: React.FC = () => {
  try {
    return (
      <div style={{ 
        padding: '20px', 
        fontFamily: 'Arial, sans-serif',
        backgroundColor: '#f0f0f0',
        minHeight: '100vh'
      }}>
        <h1>🔍 Debug AdaptEd</h1>
        <div style={{ 
          backgroundColor: '#e8f5e8', 
          color: '#2e7d32', 
          padding: '10px', 
          borderRadius: '4px',
          margin: '10px 0'
        }}>
          ✅ React está funcionando!
        </div>
        
        <div style={{ 
          backgroundColor: '#fff3e0', 
          color: '#e65100', 
          padding: '10px', 
          borderRadius: '4px',
          margin: '10px 0'
        }}>
          ⚠️ Aplicação em modo debug
        </div>

        <h2>Próximos passos:</h2>
        <ol>
          <li>Verificar console do navegador</li>
          <li>Verificar se variáveis de ambiente estão carregando</li>
          <li>Testar providers individualmente</li>
        </ol>
      </div>
    );
  } catch (error) {
    return (
      <div style={{ 
        padding: '20px', 
        backgroundColor: '#ffebee', 
        color: '#c62828' 
      }}>
        <h1>❌ Erro no Debug</h1>
        <p>{error instanceof Error ? error.message : 'Erro desconhecido'}</p>
      </div>
    );
  }
};

export default DebugApp;