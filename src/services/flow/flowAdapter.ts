/**
 * Adaptador para comunicação com a API do Flow
 */
import { FlowConfig, FlowToken, FlowModel, ChatCompletionParams, ChatCompletionResult } from '@/types/flow';
import { env } from '@/lib/env';

export class FlowAdapter {
  private baseUrl: string;
  private tenant: string;
  private clientId: string;
  private clientSecret: string;
  private appToAccess: string;
  private authBaseUrl: string;
  private token: FlowToken | null = null;

  /**
   * Inicializa o adaptador do Flow com configuração personalizada ou padrões do ambiente
   */
  constructor(config?: Partial<FlowConfig>) {
    this.baseUrl = config?.flowBaseUrl || env.VITE_FLOW_BASE_URL;
    this.tenant = config?.flowTenant || env.VITE_FLOW_TENANT;
    this.clientId = config?.flowClientId || env.VITE_FLOW_CLIENT_ID || '';
    this.clientSecret = config?.flowClientSecret || env.VITE_FLOW_CLIENT_SECRET || '';
    this.appToAccess = config?.flowAppToAccess || env.VITE_FLOW_APP_TO_ACCESS;
    this.authBaseUrl = config?.flowAuthBaseUrl || `${this.baseUrl}/auth`;
  }

  /**
   * Configura o adaptador com novas credenciais
   */
  public configure(config: Partial<FlowConfig>): void {
    if (config.flowBaseUrl) this.baseUrl = config.flowBaseUrl;
    if (config.flowTenant) this.tenant = config.flowTenant;
    if (config.flowClientId) this.clientId = config.flowClientId;
    if (config.flowClientSecret) this.clientSecret = config.flowClientSecret;
    if (config.flowAppToAccess) this.appToAccess = config.flowAppToAccess;
    if (config.flowAuthBaseUrl) this.authBaseUrl = config.flowAuthBaseUrl;
    else this.authBaseUrl = `${this.baseUrl}/auth`;
    
    // Resetar token ao reconfigurar
    this.token = null;
  }

  /**
   * Obtém a configuração atual do adaptador
   */
  public getConfig(): FlowConfig {
    return {
      flowBaseUrl: this.baseUrl,
      flowTenant: this.tenant,
      flowClientId: this.clientId,
      flowClientSecret: this.clientSecret,
      flowAppToAccess: this.appToAccess,
      flowAuthBaseUrl: this.authBaseUrl
    };
  }

  /**
   * Verifica se as credenciais necessárias estão configuradas
   */
  public isConfigured(): boolean {
    return !!(this.clientId && this.clientSecret);
  }

  /**
   * Obtém um token de acesso para a API do Flow
   */
  public async getToken(): Promise<FlowToken> {
    // Se já temos um token válido, retorna-o
    if (this.token && this.token.expiresAt > Date.now()) {
      return this.token;
    }

    if (!this.isConfigured()) {
      throw new Error('Flow não está configurado corretamente. Verifique clientId e clientSecret.');
    }

    try {
      const response = await fetch(`${this.authBaseUrl}/oauth2/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.clientId,
          client_secret: this.clientSecret,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Falha na autenticação: ${errorData.error_description || response.statusText}`);
      }

      const data = await response.json();
      
      // Calcular expiração (convertendo segundos para milissegundos)
      const expiresAt = Date.now() + data.expires_in * 1000;
      
      this.token = {
        accessToken: data.access_token,
        expiresAt,
        tokenType: data.token_type,
      };

      return this.token;
    } catch (error) {
      console.error('Erro ao obter token do Flow:', error);
      throw error;
    }
  }

  /**
   * Lista os modelos disponíveis no Flow
   */
  public async listModels(): Promise<FlowModel[]> {
    const token = await this.getToken();
    
    try {
      const response = await fetch(`${this.baseUrl}/${this.tenant}/${this.appToAccess}/llm/models`, {
        method: 'GET',
        headers: {
          'Authorization': `${token.tokenType} ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Erro ao listar modelos: ${response.statusText}`);
      }

      const data = await response.json();
      return data.models.map((model: any) => ({
        id: model.id,
        name: model.name,
        provider: model.provider,
        capabilities: model.capabilities || [],
        tokenLimit: model.token_limit,
      }));
    } catch (error) {
      console.error('Erro ao listar modelos:', error);
      throw error;
    }
  }

  /**
   * Cria uma chat completion usando o modelo especificado
   */
  public async createChatCompletion(params: ChatCompletionParams): Promise<ChatCompletionResult> {
    const token = await this.getToken();
    
    const { messages, modelId, temperature = 0.7, maxTokens, stream = false } = params;
    
    // Formatação de mensagens para a API do Flow
    const formattedMessages = messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      ...(msg.name ? { name: msg.name } : {}),
    }));

    try {
      const response = await fetch(`${this.baseUrl}/${this.tenant}/${this.appToAccess}/llm/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `${token.tokenType} ${token.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: modelId,
          messages: formattedMessages,
          temperature,
          ...(maxTokens ? { max_tokens: maxTokens } : {}),
          stream,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erro na API de chat completion: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      
      return {
        content: data.choices[0]?.message?.content || '',
        finishReason: data.choices[0]?.finish_reason || 'unknown',
        usage: data.usage ? {
          promptTokens: data.usage.prompt_tokens || 0,
          completionTokens: data.usage.completion_tokens || 0,
          totalTokens: data.usage.total_tokens || 0,
        } : undefined,
      };
    } catch (error) {
      console.error('Erro ao criar chat completion:', error);
      throw error;
    }
  }
}
