import { ChatMessage, ChatMessageRole } from '../types';

export interface FlowConfig {
  flowBaseUrl: string;
  flowTenant: string;
  flowClientId: string;
  flowClientSecret: string;
  flowAppToAccess: string;
  flowAgent?: string;
  flowAuthBaseUrl?: string;
  apiModelId?: string;
  modelTemperature?: number;
}

export interface FlowResponse {
  choices: {
    message: {
      content: string;
      role: string;
    };
    finish_reason: string;
  }[];
}

export interface FlowAuthResponse {
  access_token: string;
  expires_in: number;
}

export class FlowService {
  private config: FlowConfig;
  private token: string | null = null;
  private tokenExpiry: number = 0;

  constructor(config: FlowConfig) {
    const isDevelopment = import.meta.env.DEV;
    const baseUrl = isDevelopment ? '/flow-api' : (config.flowBaseUrl || 'https://flow.ciandt.com');
    
    this.config = {
      ...config,
      flowBaseUrl: baseUrl,
      flowAuthBaseUrl: config.flowAuthBaseUrl || baseUrl,
      flowAppToAccess: config.flowAppToAccess || 'llm-api',
      flowAgent: config.flowAgent || 'chat',
      modelTemperature: config.modelTemperature || 0.7,
    };
  }

  /**
   * Obtém um token de autenticação válido
   */
  private async getValidToken(): Promise<string> {
    const now = Date.now();

    // Renovar o token se não existir ou expirar em menos de 1 minuto
    if (!this.token || now >= this.tokenExpiry - 60000) {
      await this.refreshToken();
    }

    return this.token!;
  }

  /**
   * Renova o token de autenticação seguindo a documentação oficial
   */
  private async refreshToken(): Promise<void> {
    const url = `${this.config.flowAuthBaseUrl}/auth-engine-api/v1/api-key/token`;

    console.log("🔐 [FlowService] Iniciando autenticação", {
      url,
      tenant: this.config.flowTenant,
      clientId: this.config.flowClientId,
      appToAccess: this.config.flowAppToAccess
    });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'FlowTenant': this.config.flowTenant
        },
        body: JSON.stringify({
          clientId: this.config.flowClientId,
          clientSecret: this.config.flowClientSecret,
          appToAccess: this.config.flowAppToAccess
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ [FlowService] Falha na autenticação", {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Falha na autenticação: ${response.statusText} - ${errorText}`);
      }

      const authResponse = await response.json() as FlowAuthResponse;
      this.token = authResponse.access_token;
      this.tokenExpiry = Date.now() + (authResponse.expires_in * 1000);

      console.log("✅ [FlowService] Autenticação bem-sucedida", {
        tokenLength: this.token.length,
        expiresIn: authResponse.expires_in
      });
    } catch (error) {
      console.error("❌ [FlowService] Erro ao obter token de autenticação:", error);
      throw new Error(`Falha ao autenticar com o Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Determina o tipo de provedor com base no ID do modelo
   */
  private determineProvider(modelId: string): string {
    if (!modelId) {
      throw new Error("ID do modelo não especificado");
    }

    if (modelId.startsWith("gpt-")) {
      return "azure-openai";
    } else if (modelId.startsWith("gemini-")) {
      return "google-gemini";
    } else if (modelId.startsWith("anthropic.") || modelId.includes("claude") || modelId.startsWith("amazon.") || modelId.startsWith("meta.")) {
      return "amazon-bedrock";
    } else if (modelId === "DeepSeek-R1") {
      return "azure-foundry";
    } else if (modelId.startsWith("o1-") || modelId.startsWith("o3-")) {
      return "azure-openai";
    } else {
      // Default para OpenAI
      return "azure-openai";
    }
  }

  /**
   * Obtém o endpoint com base no provedor
   */
  private getProviderEndpoint(provider: string): string {
    switch (provider) {
      case "azure-openai":
        return "/ai-orchestration-api/v1/openai/chat/completions";
      case "google-gemini":
        return "/ai-orchestration-api/v1/google/generateContent";
      case "amazon-bedrock":
        return "/ai-orchestration-api/v1/bedrock/invoke";
      case "azure-foundry":
        return "/ai-orchestration-api/v1/foundry/chat/completions";
      default:
        return "/ai-orchestration-api/v1/openai/chat/completions";
    }
  }

  /**
   * Gera payload para Azure OpenAI
   */
  private generateOpenAIPayload(messages: ChatMessage[], modelId: string): any {
    const isO1OrO3Model = modelId.includes('o1-') || modelId.includes('o3-');

    const payload: any = {
      model: modelId,
      messages: this.transformToOpenAIMessages(messages),
      max_tokens: 4096,
      stream: false
    };

    // Modelos O1/O3 não suportam temperature mas O3 aceita reasoning_effort
    if (!isO1OrO3Model) {
      payload.temperature = this.config.modelTemperature;
    } else if (modelId.includes('o3-')) {
      payload.reasoning_effort = "medium";
    }

    console.log("🔧 [FlowService] Payload OpenAI gerado", {
      modelId,
      isO1OrO3Model,
      hasTemperature: !!payload.temperature,
      messagesCount: payload.messages.length
    });

    return payload;
  }

  /**
   * Gera payload para Google Gemini
   */
  private generateGeminiPayload(messages: ChatMessage[], modelId: string): any {
    const payload = {
      model: modelId,
      contents: this.transformToGeminiMessages(messages),
      generationConfig: {
        maxOutputTokens: 4096,
        temperature: this.config.modelTemperature
      }
    };

    console.log("🔧 [FlowService] Payload Gemini gerado", {
      modelId,
      model: payload.model,
      contentsCount: payload.contents.length
    });

    return payload;
  }

  /**
   * Gera payload para Amazon Bedrock (Claude)
   */
  private generateBedrockPayload(messages: ChatMessage[], modelId: string): any {
    // Extrair mensagem do sistema se existir
    let systemPrompt = "";
    const userMessages = messages.filter(msg => {
      if (msg.role === "system" || (msg.role === "model" && messages.indexOf(msg) === 0)) {
        systemPrompt = msg.text;
        return false;
      }
      return true;
    });

    // Payload diferentes para modelos Claude vs Nova
    if (modelId.startsWith("amazon.nova")) {
      // Formato Nova
      const payload = {
        allowedModels: [modelId],
        messages: this.transformToAnthropicMessages(userMessages),
        system: systemPrompt ? [{ text: systemPrompt }] : undefined
      };
      return payload;
    } else {
      // Formato Claude
      const payload = {
        allowedModels: [modelId],
        messages: this.transformToAnthropicMessages(userMessages),
        system: systemPrompt,
        anthropic_version: "bedrock-2023-05-31",
        max_tokens: 8192,
        temperature: this.config.modelTemperature
      };
      return payload;
    }
  }

  /**
   * Gera payload para Azure Foundry (DeepSeek)
   */
  private generateFoundryPayload(messages: ChatMessage[], modelId: string): any {
    const lastMessage = messages[messages.length - 1];
    const content = `You are an AI programming assistant, utilizing the DeepSeek Coder model, developed by DeepSeek Company, and your role is to assist with questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will not answer.
### Instruction:
${lastMessage.text}
### Response:
`;

    const payload = {
      model: modelId,
      messages: [
        {
          content: content,
          role: "user"
        }
      ]
    };

    console.log("🔧 [FlowService] Payload Foundry gerado", {
      modelId,
      lastMessageLength: lastMessage.text.length,
      contentLength: content.length
    });

    return payload;
  }

  /**
   * Transforma mensagens para formato OpenAI
   */
  private transformToOpenAIMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      role: this.mapChatRoleToOpenAI(msg.role),
      content: msg.text
    }));
  }

  /**
   * Transforma mensagens para formato Gemini
   */
  private transformToGeminiMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      role: this.mapChatRoleToGemini(msg.role),
      parts: [{ text: msg.text }]
    }));
  }

  /**
   * Transforma mensagens para formato Anthropic
   */
  private transformToAnthropicMessages(messages: ChatMessage[]): any[] {
    return messages.map(msg => ({
      role: this.mapChatRoleToAnthropic(msg.role),
      content: [{ type: "text", text: msg.text }]
    }));
  }

  /**
   * Mapeia funções de role para OpenAI
   */
  private mapChatRoleToOpenAI(role: ChatMessageRole): string {
    switch (role) {
      case "user": return "user";
      case "model": case "assistant": return "assistant";
      case "system": return "system";
      default: return "user";
    }
  }

  /**
   * Mapeia funções de role para Gemini
   */
  private mapChatRoleToGemini(role: ChatMessageRole): string {
    switch (role) {
      case "user": return "user";
      case "model": case "assistant": return "model";
      case "system": return "user"; // Gemini não tem role "system", usar "user"
      default: return "user";
    }
  }

  /**
   * Mapeia funções de role para Anthropic
   */
  private mapChatRoleToAnthropic(role: ChatMessageRole): string {
    switch (role) {
      case "user": return "user";
      case "model": case "assistant": return "assistant";
      case "system": return "user"; // Já tratado separadamente no payload
      default: return "user";
    }
  }

  /**
   * Lista modelos disponíveis
   */
  async listModels(provider: string = "azure-openai"): Promise<any[]> {
    const token = await this.getValidToken();
    const url = `${this.config.flowBaseUrl}/ai-orchestration-api/v1/models/${provider}?capabilities=system-instruction,chat-conversation`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'FlowTenant': this.config.flowTenant,
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Falha ao obter modelos: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Erro ao listar modelos:", error);
      throw new Error(`Falha ao listar modelos: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Cria completions de chat
   */
  async createChatCompletion(messages: ChatMessage[], modelId: string = this.config.apiModelId || "gpt-4o-mini"): Promise<string> {
    const token = await this.getValidToken();
    const provider = this.determineProvider(modelId);
    const endpoint = this.getProviderEndpoint(provider);
    const url = `${this.config.flowBaseUrl}${endpoint}`;

    console.log("🚀 [FlowService] createChatCompletion iniciado", {
      modelId,
      modelIdType: typeof modelId,
      messagesCount: messages.length,
      provider,
      endpoint,
      url
    });

    // Validar mensagens antes de processar
    if (!messages || messages.length === 0) {
      throw new Error("Nenhuma mensagem fornecida");
    }

    // Verificar se todas as mensagens têm conteúdo
    const invalidMessages = messages.filter(msg => !msg.text || msg.text.trim() === '');
    if (invalidMessages.length > 0) {
      console.error("❌ [FlowService] Mensagens inválidas encontradas:", invalidMessages);
      throw new Error("Mensagens com conteúdo vazio encontradas");
    }

    let payload;
    switch (provider) {
      case "azure-openai":
        payload = this.generateOpenAIPayload(messages, modelId);
        break;
      case "google-gemini":
        payload = this.generateGeminiPayload(messages, modelId);
        break;
      case "amazon-bedrock":
        payload = this.generateBedrockPayload(messages, modelId);
        break;
      case "azure-foundry":
        payload = this.generateFoundryPayload(messages, modelId);
        break;
      default:
        payload = this.generateOpenAIPayload(messages, modelId);
    }

    console.log("📤 [FlowService] Enviando requisição", {
      provider,
      payloadKeys: Object.keys(payload),
      messagesInPayload: payload.messages?.length || payload.contents?.length || 0
    });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`,
          'FlowTenant': this.config.flowTenant,
          'FlowAgent': this.config.flowAgent || 'chat'
        },
        body: JSON.stringify(payload)
      });

      console.log("📥 [FlowService] Resposta recebida", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("❌ [FlowService] Erro na resposta", {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`Falha ao obter resposta: ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();
      console.log("📋 [FlowService] Dados da resposta", {
        provider,
        dataKeys: Object.keys(data),
        hasChoices: !!data.choices,
        hasCandidates: !!data.candidates
      });

      // Extrai o conteúdo da resposta com base no provedor
      let responseContent = "";
      if (provider === "azure-openai") {
        responseContent = data.choices?.[0]?.message?.content || "";
      } else if (provider === "google-gemini") {
        responseContent = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
      } else if (provider === "amazon-bedrock") {
        responseContent = data.content?.[0]?.text || "";
      } else if (provider === "azure-foundry") {
        responseContent = data.choices?.[0]?.message?.content || "";
      }

      if (!responseContent) {
        console.error("❌ [FlowService] Resposta vazia", { data, provider });
        return "Não foi possível obter uma resposta válida.";
      }

      console.log("✅ [FlowService] Resposta extraída com sucesso", {
        contentLength: responseContent.length,
        provider
      });

      return responseContent;
    } catch (error) {
      console.error("❌ [FlowService] Erro ao criar chat completion:", error);
      throw new Error(`Falha na comunicação com o Flow: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }
}
