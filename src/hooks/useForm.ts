/**
 * Hook para gerenciar formulários com validação e tratamento de erros
 */
import { useState, useCallback, ChangeEvent } from 'react';
import { z } from 'zod';

type ValidationSchema<T> = z.ZodType<T>;
type ValidationErrors<T> = Partial<Record<keyof T, string>>;
type TouchedFields<T> = Partial<Record<keyof T, boolean>>;

interface UseFormOptions<T> {
  initialValues: T;
  validationSchema?: ValidationSchema<T>;
  onSubmit?: (values: T) => void | Promise<void>;
}

export function useForm<T extends Record<string, any>>({
  initialValues,
  validationSchema,
  onSubmit,
}: UseFormOptions<T>) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<ValidationErrors<T>>({});
  const [touched, setTouched] = useState<TouchedFields<T>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValid, setIsValid] = useState(false);

  // Valida todos os valores ou um campo específico
  const validate = useCallback(
    (fieldValues: T = values): ValidationErrors<T> => {
      if (!validationSchema) return {};

      try {
        // Valida os valores usando o schema Zod
        validationSchema.parse(fieldValues);
        setIsValid(true);
        return {};
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Formata os erros do Zod para nosso formato de erros
          const formattedErrors = error.errors.reduce((acc, curr) => {
            const path = curr.path[0] as keyof T;
            return {
              ...acc,
              [path]: curr.message,
            };
          }, {} as ValidationErrors<T>);
          
          setIsValid(false);
          return formattedErrors;
        }
        return {};
      }
    },
    [values, validationSchema]
  );

  // Manipulador de mudança para campos de formulário
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value, type } = e.target;
      
      // Para checkboxes e radio buttons
      const val = type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : value;
      
      setValues((prev) => ({ ...prev, [name]: val }));
      
      // Marca o campo como tocado
      setTouched((prev) => ({ ...prev, [name]: true }));
      
      // Valida o campo se já foi tocado
      if (validationSchema && touched[name as keyof T]) {
        const fieldErrors = validate({ ...values, [name]: val });
        setErrors((prev) => ({ ...prev, ...fieldErrors }));
      }
    },
    [values, touched, validate, validationSchema]
  );

  // Define um valor específico
  const setFieldValue = useCallback(
    (name: keyof T, value: any) => {
      setValues((prev) => ({ ...prev, [name]: value }));
      
      // Marca o campo como tocado
      setTouched((prev) => ({ ...prev, [name]: true }));
      
      // Valida o campo se já foi tocado
      if (validationSchema && touched[name]) {
        const fieldErrors = validate({ ...values, [name]: value });
        setErrors((prev) => ({ ...prev, ...fieldErrors }));
      }
    },
    [values, touched, validate, validationSchema]
  );

  // Marca um campo como tocado
  const setFieldTouched = useCallback(
    (name: keyof T, isTouched = true) => {
      setTouched((prev) => ({ ...prev, [name]: isTouched }));
      
      // Valida o campo se foi marcado como tocado
      if (validationSchema && isTouched) {
        const fieldErrors = validate(values);
        setErrors((prev) => ({ ...prev, ...fieldErrors }));
      }
    },
    [values, validate, validationSchema]
  );

  // Manipulador de envio do formulário
  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }
      
      // Marca todos os campos como tocados
      const touchedFields = Object.keys(values).reduce(
        (acc, key) => ({ ...acc, [key]: true }),
        {} as TouchedFields<T>
      );
      setTouched(touchedFields);
      
      // Valida todos os campos
      const validationErrors = validate();
      setErrors(validationErrors);
      
      // Se não houver erros e onSubmit for fornecido, envia o formulário
      if (Object.keys(validationErrors).length === 0 && onSubmit) {
        setIsSubmitting(true);
        try {
          await onSubmit(values);
        } finally {
          setIsSubmitting(false);
        }
      }
    },
    [values, validate, onSubmit]
  );

  // Reseta o formulário para os valores iniciais
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
    setIsValid(false);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleSubmit,
    setFieldValue,
    setFieldTouched,
    resetForm,
  };
}

export default useForm;
