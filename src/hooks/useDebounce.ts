/**
 * Hook para debounce de valores, útil para entradas que mudam frequentemente
 */
import { useState, useEffect } from 'react';

/**
 * Retorna uma versão com debounce do valor fornecido
 * @param value Valor a ser "debouncado"
 * @param delay Tempo de espera em milissegundos
 * @returns Valor com debounce aplicado
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Configura timer para atualizar o valor após o delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Limpa o timer se o valor ou delay mudar
    // ou quando o componente for desmontado
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export default useDebounce;
