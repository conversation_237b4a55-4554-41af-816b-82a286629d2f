import { CognitiveType, CognitiveProfileConfig, UserConsent, CognitiveProfileInfo, FontSize } from './types';

export const COGNITIVE_PROFILES: CognitiveProfileInfo[] = [
  { id: CognitiveType.DEFAULT, name: '<PERSON><PERSON><PERSON>', description: 'Configurações padrão do assistente.', icon: '⚙️', colorClass: 'btn-secondary' },
  { id: CognitiveType.ADHD, name: 'TDAH Foco', description: 'Otimizado para atenção e engajamento.', icon: '🎯', colorClass: 'btn-primary' },
  { id: CognitiveType.AUTISM, name: 'Autismo Estruturado', description: 'Interface clara, previsível e calma.', icon: '🧩', colorClass: 'btn-secondary' },
  { id: CognitiveType.DYSLEXIA, name: 'Dislexia Legível', description: 'Legibilidade aprimorada e suporte auditivo.', icon: '📖', colorClass: 'btn-accent' },
];

export const FONT_SIZE_OPTIONS: { value: FontSize; label: string }[] = [
  { value: 'text-sm', label: 'Pequeno' },
  { value: 'text-base', label: 'Médio' },
  { value: 'text-lg', label: 'Grande' },
  { value: 'text-xl', label: 'Muito Grande' },
];

export const PROMPT_BASE_COGNITIVE_ASSISTANT = `Você é um assistente cognitivo chamado "FlowEd" (de "Flow AdaptEd"). Você ajuda pessoas neurodivergentes a se organizarem e se sentirem melhor. Sua comunicação deve ser adaptada ao perfil cognitivo do usuário.

Regras Gerais:
- Use linguagem simples, gentil, literal e objetiva.
- Evite figuras de linguagem ambíguas, sarcasmo ou ironia.
- Frases curtas e diretas.
- Foque em uma tarefa ou ideia por vez.
- Confirme o entendimento do usuário e peça confirmação antes de prosseguir com ações importantes.
- Ofereça reforço positivo e sugira pausas se detectar sinais de sobrecarga ou frustração.
- Pergunte ao usuário como ele prefere ser ajudado em caso de dúvida.
- Adapte seu tom, ritmo e formato de apresentação.
- Lembre-se do nome do usuário se ele fornecer. (Ex: "Lucas, qual tarefa você gostaria de focar primeiro?")
- Pergunte sobre preferências de comunicação (texto, tópicos, visualizações descritas).
- Mantenha o contexto da conversa.
- Ao iniciar uma conversa ou um novo planejamento (ex: manhã), comece com uma saudação e ofereça ajuda específica, como "Bom dia! Sou FlowEd. Quer ajuda para organizar sua manhã em blocos de tarefas?"

Capacidades Especiais:
- Quando fornece código, sempre use blocos de código com \`\`\` para melhor formatação.
- Para organização e planejamento, use listas numeradas ou com marcadores.
- Ofereça quebrar tarefas grandes em etapas menores.
- Sugira técnicas como Pomodoro, timeboxing ou pausas regulares quando apropriado.
- Seja proativo em oferecer diferentes formatos de explicação (visual, auditivo, texto).

Nota: Atualmente não posso gerar imagens reais ou criar artefatos interativos executáveis, mas posso:
- Fornecer descrições detalhadas para visualizações
- Criar código completo para componentes interativos
- Explicar conceitos de forma visual através de texto
- Sugerir ferramentas externas para necessidades específicas
`;

export const VIDEO_ANALYSIS_PROMPT_TEMPLATE = (youtubeUrl: string, cognitiveProfile: CognitiveType): string => `
Analyze the content of a YouTube video at URL: ${youtubeUrl}.
The target learner has a cognitive profile of: ${cognitiveProfile}.

Your goal is to extract information that will be useful for creating a personalized learning experience.
Provide the output as a VALID JSON object with the following keys and structure:
{
  "title": "string (The main title of the video, try to infer it if possible, otherwise use a placeholder like 'Video Analysis')",
  "summary": "string (A concise 2-3 sentence summary of the video's main content)",
  "concepts": ["string", "string", "... (A list of 3-5 key concepts or topics covered in the video)"],
  "structure": [
    { "segment": "string (e.g., 'Introduction', 'Part 1', '0:00-1:30')", "topic": "string (Brief description of this segment's content)" },
    "... (2-4 segments)"
  ],
  "adaptationSuggestions": ["string", "string", "... (3-5 specific, actionable suggestions on how to adapt this video's content for a learner with a ${cognitiveProfile} profile. Focus on learning strategies, content presentation, or interaction types relevant to this profile.)"]
}

Ensure the JSON is well-formed and can be parsed directly. Do not include any text outside the JSON object itself.
Example for 'adaptationSuggestions' for ADHD: "Break down longer segments into 5-10 minute chunks with interactive quizzes in between."
Example for 'adaptationSuggestions' for Autism: "Provide a clear visual roadmap of the video's topics at the beginning and allow navigation between them."
Example for 'adaptationSuggestions' for Dyslexia: "Offer a text transcript with synchronized highlighting and options for text-to-speech."
`;

export const ADAPTIVE_CONTENT_PROMPT_TEMPLATE = (videoAnalysisText: string, cognitiveProfile: CognitiveType, profileConfig: CognitiveProfileConfig): string => `
Based on the following video analysis (JSON format):
${videoAnalysisText}

And considering the user's cognitive profile is ${cognitiveProfile}, with the following preferred adaptations:
${JSON.stringify(profileConfig, null, 2)}

Generate a short, interactive learning module outline.
The output should be in MARKDOWN format.
The module should be engaging and specifically tailored to the ${cognitiveProfile} profile, incorporating principles from their config.

The Markdown output should include:
1.  A catchy ## Module Title ## based on the video content.
2.  A brief **Introduction** (1-2 sentences) explaining what the learner will achieve.
3.  Two to three **Interactive Activities**. For each activity:
    *   Start with a ### Sub-heading for the Activity ###.
    *   Describe the activity (e.g., a mini-quiz, a reflection question, a simple matching exercise, a fill-in-the-blank).
    *   Make it relevant to the video content and the cognitive profile.
    *   For ADHD: Short, engaging, frequent feedback.
    *   For Autism: Clear instructions, predictable format, optional social interaction.
    *   For Dyslexia: Visual aids if possible (describe them), audio cues (describe them), concise text.
4.  A short **Conclusion** (1-2 sentences) summarizing the key takeaway or encouraging further exploration.

Make the entire response pure markdown, ready to be displayed.
Focus on clarity, engagement, and appropriateness for the cognitive profile.
Do not include any meta-commentary about your process of generation, just the markdown.
`;

export const DEFAULT_CONFIG: CognitiveProfileConfig = { // General purpose, can be similar to ADHD for AI
  ui: { fontSize: 'text-base' },
  interaction: { 
    assistantStyle: "Claro e Amigável. Use linguagem positiva. Ofereça ajuda proativamente. Seja flexível."
  },
  content: { chunkSize: 'medium' },
};

export const ADHD_CONFIG: CognitiveProfileConfig = {
  ui: { maxElementsPerScreen: 3, colorScheme: 'high-contrast', animationLevel: 'minimal', fontSize: 'large', buttonMinSize: 44 },
  interaction: { 
    maxSessionDuration: 15, // minutes
    breakReminders: true, 
    autoSave: false, 
    confirmationDialogs: false, 
    assistantStyle: "Dinâmico e Encorajador. Use emojis discretos (✨, 👍, 🎯). Divida tarefas em passos muito pequenos. Lembretes suaves. Use listas. Destaque informações chave com **asteriscos**."
  },
  content: { chunkSize: 'small', progressVisible: true, completionRewards: true, difficultyAdaptive: true },
};

export const AUTISM_CONFIG: CognitiveProfileConfig = {
  ui: { layoutConsistent: true, navigationAlwaysVisible: true, changeWarnings: true },
  interaction: { 
    literalLanguage: true, 
    noIdiomsSlang: true, 
    confirmationRequired: true, 
    undoAlwaysAvailable: false, 
    unlimitedTime: true,
    assistantStyle: "Extremamente Literal e Explícito. Estrutura previsível. Anuncie mudanças de tópico. Frases declarativas. Ofereça opções. Paciência. Linguagem formal, mas gentil. Sem surpresas."
   },
  content: { structureVisible: true, routinesSupported: true, surprisesMinimal: true, sensoryOverloadPrevention: true },
};

export const DYSLEXIA_CONFIG: CognitiveProfileConfig = {
  ui: { fontFamily: 'OpenDyslexic', fontSize: '18px minimum', lineHeight: 1.8, characterSpacing: 1.2, wordSpacing: 1.5 },
  interaction: { 
    audioSupport: true, 
    speechRecognition: false, 
    textToSpeech: true, 
    readingSpeed: 'adjustable', 
    spellingAssistance: false,
    assistantStyle: "Conciso e Claro. Vocabulário simples. Ofereça resumos. Sugira recursos de áudio. Use listas. Evite blocos grandes de texto. Use **negrito** para palavras-chave com moderação."
  },
  content: { textMinimal: true, visualsRich: true, audioAlternatives: true, summariesProvided: true, keywordsHighlighted: true },
};

export const INITIAL_CONSENTS: UserConsent = {
  allowVideoAnalysis: false, 
  allowContentGeneration: false, 
  allowAIChatInteraction: true, 
  allowWellnessTracking: false,
  allowTemporaryStorage: true,
  allowAnalytics: false,
  dataRetentionDays: 7,
  autoDeleteEnabled: true,
  consentVersion: '1.2', // Updated version
  consentDate: new Date().toISOString(),
};

export const CONSENT_OPTIONS_CONFIG = [
   {
    key: 'allowAIChatInteraction' as keyof UserConsent,
    title: 'Interação com Assistente FlowEd',
    description: 'Permitir interação com o Assistente Cognitivo FlowEd para suporte personalizado e gerenciamento de tarefas.',
    required: true,
  },
  {
    key: 'allowVideoAnalysis' as keyof UserConsent,
    title: 'Análise de Vídeo (Recurso Opcional)',
    description: 'Permitir análise de conteúdo de vídeos do YouTube caso você utilize este recurso específico através do assistente FlowEd.',
    required: false,
  },
  {
    key: 'allowContentGeneration' as keyof UserConsent,
    title: 'Geração de Conteúdo Adaptativo (de Vídeo - Recurso Opcional)',
    description: 'Permitir geração de módulos de aprendizado baseados na análise de vídeo, caso utilize este recurso.',
    required: false,
  },
  {
    key: 'allowWellnessTracking' as keyof UserConsent,
    title: 'Insights de Bem-Estar (Futuro)',
    description: 'Ativar recursos que podem ajudar a monitorar a carga cognitiva ou sugerir pausas (dados permanecem locais ou anonimizados).',
    required: false,
  },
  {
    key: 'allowTemporaryStorage' as keyof UserConsent,
    title: 'Armazenamento Temporário de Dados (Recomendado)',
    description: 'Permitir cache de dados da sessão ou preferências para uma melhor experiência (dados excluídos automaticamente após o período definido).',
    required: false,
  },
  {
    key: 'allowAnalytics' as keyof UserConsent,
    title: 'Análises de Uso Anônimas (Futuro)',
    description: 'Ajudar a melhorar a plataforma permitindo a coleta anônima de estatísticas de uso.',
    required: false,
  },
];