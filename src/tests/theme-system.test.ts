/**
 * Testes para o sistema de temas refatorado
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  getPersonaConfig, 
  getThemeClass, 
  applyPersonaStyles,
  PERSONA_CONFIGS 
} from '@/lib/theme';
import { CognitivePersona } from '@/types/theme';

// Mock do DOM para testes
const mockDocument = {
  documentElement: {
    setAttribute: vi.fn(),
    style: {
      setProperty: vi.fn()
    }
  }
};

// Mock global do document
Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true
});

describe('Sistema de Temas Refatorado', () => {
  beforeEach(() => {
    // Limpar mocks antes de cada teste
    vi.clearAllMocks();
  });

  describe('Configurações de Persona', () => {
    it('deve ter todas as personas configuradas', () => {
      expect(PERSONA_CONFIGS).toHaveLength(4);
      
      const personaIds = PERSONA_CONFIGS.map(config => config.id);
      expect(personaIds).toContain('DEFAULT');
      expect(personaIds).toContain('ADHD');
      expect(personaIds).toContain('AUTISM');
      expect(personaIds).toContain('DYSLEXIA');
    });

    it('deve retornar configuração correta para cada persona', () => {
      const adhdConfig = getPersonaConfig('ADHD');
      expect(adhdConfig.id).toBe('ADHD');
      expect(adhdConfig.name).toBe('ADHD Focus');
      expect(adhdConfig.icon).toBe('🎯');
      
      const autismConfig = getPersonaConfig('AUTISM');
      expect(autismConfig.id).toBe('AUTISM');
      expect(autismConfig.name).toBe('Autism Structured');
      expect(autismConfig.icon).toBe('🧩');
      
      const dyslexiaConfig = getPersonaConfig('DYSLEXIA');
      expect(dyslexiaConfig.id).toBe('DYSLEXIA');
      expect(dyslexiaConfig.name).toBe('Dyslexia Readable');
      expect(dyslexiaConfig.icon).toBe('📖');
    });

    it('deve retornar configuração padrão para persona inválida', () => {
      const config = getPersonaConfig('INVALID' as CognitivePersona);
      expect(config.id).toBe('DEFAULT');
    });
  });

  describe('Classes de Tema', () => {
    it('deve gerar classes de background corretas', () => {
      expect(getThemeClass('background', 'primary')).toBe('bg-theme-primary');
      expect(getThemeClass('background', 'secondary')).toBe('bg-theme-secondary');
      expect(getThemeClass('background', 'tertiary')).toBe('bg-theme-tertiary');
      expect(getThemeClass('background', 'quaternary')).toBe('bg-theme-quaternary');
    });

    it('deve gerar classes de texto corretas', () => {
      expect(getThemeClass('text', 'primary')).toBe('text-theme-primary');
      expect(getThemeClass('text', 'secondary')).toBe('text-theme-secondary');
      expect(getThemeClass('text', 'muted')).toBe('text-theme-muted');
      expect(getThemeClass('text', 'inverse')).toBe('text-theme-inverse');
    });

    it('deve gerar classes de botão corretas', () => {
      expect(getThemeClass('button', 'primary')).toBe('btn-primary');
      expect(getThemeClass('button', 'secondary')).toBe('btn-secondary');
      expect(getThemeClass('button', 'accent')).toBe('btn-accent');
      expect(getThemeClass('button', 'success')).toBe('btn-success');
      expect(getThemeClass('button', 'warning')).toBe('btn-warning');
      expect(getThemeClass('button', 'error')).toBe('btn-error');
    });

    it('deve gerar classes de borda corretas', () => {
      expect(getThemeClass('border', 'default')).toBe('border-theme');
      expect(getThemeClass('border', 'light')).toBe('border-theme-light');
      expect(getThemeClass('border', 'dark')).toBe('border-theme-dark');
      expect(getThemeClass('border', 'focus')).toBe('border-theme-focus');
    });

    it('deve gerar classes de card corretas', () => {
      expect(getThemeClass('card', 'default')).toBe('card-theme');
      expect(getThemeClass('card', 'elevated')).toBe('card-theme-elevated');
    });

    it('deve gerar classes de input corretas', () => {
      expect(getThemeClass('input', 'default')).toBe('input-theme');
      expect(getThemeClass('input', 'success')).toBe('input-success');
      expect(getThemeClass('input', 'warning')).toBe('input-warning');
      expect(getThemeClass('input', 'error')).toBe('input-error');
    });

    it('deve retornar string vazia para tipo/variante inválidos', () => {
      expect(getThemeClass('invalid' as any, 'primary')).toBe('');
      expect(getThemeClass('button', 'invalid')).toBe('');
    });
  });

  describe('Aplicação de Estilos de Persona', () => {
    it('deve aplicar atributos de dados corretos', () => {
      applyPersonaStyles('ADHD', 'light');
      
      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith('data-persona', 'ADHD');
      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'light');
    });

    it('deve aplicar estilos específicos da persona DYSLEXIA', () => {
      applyPersonaStyles('DYSLEXIA', 'light');
      
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--font-family-override', 
        'OpenDyslexic'
      );
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--line-height-override', 
        '1.8'
      );
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--letter-spacing-override', 
        '0.05em'
      );
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--word-spacing-override', 
        '0.1em'
      );
    });

    it('deve aplicar estilos específicos da persona ADHD', () => {
      applyPersonaStyles('ADHD', 'dark');
      
      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith('data-persona', 'ADHD');
      expect(mockDocument.documentElement.setAttribute).toHaveBeenCalledWith('data-theme', 'dark');
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--font-size-override', 
        'lg'
      );
    });
  });

  describe('Configurações de UI por Persona', () => {
    it('deve ter configurações específicas para ADHD', () => {
      const config = getPersonaConfig('ADHD');
      expect(config.uiPreferences.fontSize).toBe('lg');
      expect(config.uiPreferences.borderRadius).toBe('lg');
      expect(config.uiPreferences.animationLevel).toBe('reduced');
      expect(config.uiPreferences.buttonMinSize).toBe(44);
      expect(config.uiPreferences.maxElementsPerScreen).toBe(3);
    });

    it('deve ter configurações específicas para AUTISM', () => {
      const config = getPersonaConfig('AUTISM');
      expect(config.uiPreferences.fontSize).toBe('base');
      expect(config.uiPreferences.borderRadius).toBe('sm');
      expect(config.uiPreferences.animationLevel).toBe('none');
      expect(config.uiPreferences.fontFamily).toBe('system-ui');
    });

    it('deve ter configurações específicas para DYSLEXIA', () => {
      const config = getPersonaConfig('DYSLEXIA');
      expect(config.uiPreferences.fontSize).toBe('lg');
      expect(config.uiPreferences.fontFamily).toBe('OpenDyslexic');
      expect(config.uiPreferences.lineHeight).toBe(1.8);
      expect(config.uiPreferences.letterSpacing).toBe(0.05);
      expect(config.uiPreferences.wordSpacing).toBe(0.1);
      expect(config.uiPreferences.borderRadius).toBe('md');
    });
  });

  describe('Esquemas de Cores por Persona', () => {
    it('deve ter cores específicas para ADHD', () => {
      const config = getPersonaConfig('ADHD');
      
      // Light mode
      expect(config.colorScheme.light.primary).toBe('59, 130, 246');
      expect(config.colorScheme.light.accent).toBe('16, 185, 129');
      expect(config.colorScheme.light.backgroundPrimary).toBe('248, 250, 252');
      
      // Dark mode
      expect(config.colorScheme.dark.primary).toBe('96, 165, 250');
      expect(config.colorScheme.dark.accent).toBe('52, 211, 153');
      expect(config.colorScheme.dark.backgroundPrimary).toBe('15, 23, 42');
    });

    it('deve ter cores específicas para AUTISM', () => {
      const config = getPersonaConfig('AUTISM');
      
      // Light mode
      expect(config.colorScheme.light.primary).toBe('100, 116, 139');
      expect(config.colorScheme.light.accent).toBe('139, 92, 246');
      
      // Dark mode
      expect(config.colorScheme.dark.primary).toBe('148, 163, 184');
      expect(config.colorScheme.dark.accent).toBe('196, 181, 253');
    });

    it('deve ter cores específicas para DYSLEXIA', () => {
      const config = getPersonaConfig('DYSLEXIA');
      
      // Light mode
      expect(config.colorScheme.light.primary).toBe('34, 197, 94');
      expect(config.colorScheme.light.accent).toBe('245, 158, 11');
      expect(config.colorScheme.light.backgroundPrimary).toBe('255, 254, 247');
      expect(config.colorScheme.light.textPrimary).toBe('23, 23, 23');
      
      // Dark mode
      expect(config.colorScheme.dark.primary).toBe('74, 222, 128');
      expect(config.colorScheme.dark.accent).toBe('251, 191, 36');
      expect(config.colorScheme.dark.backgroundPrimary).toBe('41, 37, 36');
      expect(config.colorScheme.dark.textPrimary).toBe('250, 250, 249');
    });
  });
});
