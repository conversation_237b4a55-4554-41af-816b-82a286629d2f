/**
 * Testes de conectividade com a API Flow
 * Execute apenas quando VITE_FLOW_CLIENT_SECRET estiver configurado
 */
import { describe, it, expect, beforeAll } from 'vitest';
import { FlowService } from '@/services/flowService';
import { env } from '@/lib/env';

describe('Flow API Connectivity', () => {
  let flowService: FlowService;
  let hasFlowCredentials: boolean;

  beforeAll(() => {
    hasFlowCredentials = !!(
      env.VITE_FLOW_CLIENT_ID && 
      env.VITE_FLOW_CLIENT_SECRET && 
      env.VITE_FLOW_CLIENT_SECRET !== ''
    );

    if (hasFlowCredentials) {
      flowService = new FlowService({
        flowBaseUrl: env.VITE_FLOW_BASE_URL,
        flowTenant: env.VITE_FLOW_TENANT,
        flowClientId: env.VITE_FLOW_CLIENT_ID!,
        flowClientSecret: env.VITE_FLOW_CLIENT_SECRET!,
        flowAppToAccess: env.VITE_FLOW_APP_TO_ACCESS,
      });
    }
  });

  it('deve verificar se credenciais Flow estão disponíveis', () => {
    if (!hasFlowCredentials) {
      console.warn('🔒 Credenciais Flow não configuradas - pulando testes de conectividade');
      console.warn('   Configure VITE_FLOW_CLIENT_SECRET para executar estes testes');
      expect(true).toBe(true); // Não falha, apenas informa
      return;
    }
    
    expect(env.VITE_FLOW_CLIENT_ID).toBeDefined();
    expect(env.VITE_FLOW_CLIENT_SECRET).toBeDefined();
    expect(env.VITE_FLOW_CLIENT_SECRET).not.toBe('');
  });

  it('deve conseguir autenticar com Flow API', async () => {
    if (!hasFlowCredentials) {
      console.log('⏭️  Pulando teste de autenticação - credenciais não configuradas');
      return;
    }

    try {
      // Tenta listar modelos para verificar autenticação
      const models = await flowService.listModels('azure-openai');
      expect(Array.isArray(models)).toBe(true);
      console.log(`✅ Flow conectado com sucesso! ${models.length} modelos encontrados`);
    } catch (error) {
      console.error('❌ Erro ao conectar com Flow:', error);
      throw error;
    }
  }, 30000); // 30s timeout

  it('deve conseguir listar modelos de diferentes provedores', async () => {
    if (!hasFlowCredentials) {
      console.log('⏭️  Pulando teste de modelos - credenciais não configuradas');
      return;
    }

    const providers = ['azure-openai', 'google-gemini', 'amazon-bedrock'];
    const results = [];

    for (const provider of providers) {
      try {
        const models = await flowService.listModels(provider);
        results.push({ provider, count: models.length, success: true });
        console.log(`✅ ${provider}: ${models.length} modelos`);
      } catch (error) {
        results.push({ provider, error: error.message, success: false });
        console.warn(`⚠️  ${provider}: ${error.message}`);
      }
    }

    // Pelo menos um provedor deve funcionar
    const successfulProviders = results.filter(r => r.success);
    expect(successfulProviders.length).toBeGreaterThan(0);
  }, 45000); // 45s timeout para múltiplos provedores
});