/**
 * Testes para validar configuração completa do projeto
 */
import { describe, it, expect } from 'vitest';
import { env } from '@/lib/env';

describe('Validação de Configuração', () => {
  describe('Firebase Configuration', () => {
    it('deve ter todas as variáveis Firebase obrigatórias', () => {
      expect(env.VITE_FIREBASE_API_KEY).toBeDefined();
      expect(env.VITE_FIREBASE_API_KEY).not.toBe('');
      expect(env.VITE_FIREBASE_AUTH_DOMAIN).toBeDefined();
      expect(env.VITE_FIREBASE_PROJECT_ID).toBeDefined();
      expect(env.VITE_FIREBASE_STORAGE_BUCKET).toBeDefined();
      expect(env.VITE_FIREBASE_MESSAGING_SENDER_ID).toBeDefined();
      expect(env.VITE_FIREBASE_APP_ID).toBeDefined();
    });

    it('deve ter formato correto para variáveis Firebase', () => {
      // API Key deve começar com AIza
      expect(env.VITE_FIREBASE_API_KEY).toMatch(/^AIza/);
      
      // Auth Domain deve ter formato correto
      expect(env.VITE_FIREBASE_AUTH_DOMAIN).toMatch(/\.firebaseapp\.com$/);
      
      // App ID deve ter formato correto
      expect(env.VITE_FIREBASE_APP_ID).toMatch(/^\d+:\d+:web:/);
    });
  });

  describe('Flow Configuration', () => {
    it('deve ter URL base válida', () => {
      expect(env.VITE_FLOW_BASE_URL).toBeDefined();
      expect(env.VITE_FLOW_BASE_URL).toMatch(/^https?:\/\//);
    });

    it('deve ter tenant definido', () => {
      expect(env.VITE_FLOW_TENANT).toBeDefined();
      expect(env.VITE_FLOW_TENANT).not.toBe('');
    });

    it('deve ter Client ID em formato UUID', () => {
      if (env.VITE_FLOW_CLIENT_ID) {
        expect(env.VITE_FLOW_CLIENT_ID).toMatch(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        );
      }
    });

    it('deve avisar se Client Secret está vazio', () => {
      if (!env.VITE_FLOW_CLIENT_SECRET || env.VITE_FLOW_CLIENT_SECRET === '') {
        console.warn('⚠️  VITE_FLOW_CLIENT_SECRET não configurado - Flow funcionará apenas via UI');
      }
      // Não falha o teste, apenas avisa
      expect(true).toBe(true);
    });
  });
});