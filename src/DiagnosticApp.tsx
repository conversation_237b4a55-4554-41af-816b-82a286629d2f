import React from 'react';

const DiagnosticApp: React.FC = () => {
  const [step, setStep] = React.useState(0);
  const [errors, setErrors] = React.useState<string[]>([]);

  const tests = [
    {
      name: 'Importar env',
      test: async () => {
        const { env } = await import('./lib/env');
        return `Firebase Project: ${env.VITE_FIREBASE_PROJECT_ID}`;
      }
    },
    {
      name: 'Importar Firebase',
      test: async () => {
        const firebase = await import('./services/firebase');
        return 'Firebase importado com sucesso';
      }
    },
    {
      name: 'Importar AuthProvider',
      test: async () => {
        const auth = await import('./components/AuthProvider');
        return 'AuthProvider importado com sucesso';
      }
    },
    {
      name: 'Importar FlowProvider',
      test: async () => {
        const flow = await import('./components/FlowProvider');
        return 'FlowProvider importado com sucesso';
      }
    }
  ];

  React.useEffect(() => {
    const runTests = async () => {
      for (let i = 0; i < tests.length; i++) {
        try {
          const result = await tests[i].test();
          console.log(`✅ ${tests[i].name}: ${result}`);
          setStep(i + 1);
          await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
          const errorMsg = `❌ ${tests[i].name}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
          console.error(errorMsg);
          setErrors(prev => [...prev, errorMsg]);
          break;
        }
      }
    };

    runTests();
  }, []);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      backgroundColor: '#f0f0f0',
      minHeight: '100vh'
    }}>
      <h1>🏥 Diagnóstico AdaptEd</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <strong>Progresso: {step}/{tests.length}</strong>
        <div style={{ 
          backgroundColor: '#ddd', 
          height: '10px', 
          borderRadius: '5px',
          marginTop: '5px'
        }}>
          <div style={{ 
            backgroundColor: '#4caf50', 
            height: '10px', 
            borderRadius: '5px',
            width: `${(step / tests.length) * 100}%`,
            transition: 'width 0.5s'
          }} />
        </div>
      </div>

      <h2>Testes:</h2>
      {tests.map((test, index) => (
        <div key={index} style={{
          padding: '10px',
          margin: '5px 0',
          borderRadius: '4px',
          backgroundColor: 
            index < step ? '#e8f5e8' : 
            index === step ? '#fff3e0' : '#f5f5f5',
          color: 
            index < step ? '#2e7d32' : 
            index === step ? '#e65100' : '#666'
        }}>
          {index < step ? '✅' : index === step ? '⏳' : '⏸️'} {test.name}
        </div>
      ))}

      {errors.length > 0 && (
        <div>
          <h2>❌ Erros Encontrados:</h2>
          {errors.map((error, index) => (
            <div key={index} style={{
              backgroundColor: '#ffebee',
              color: '#c62828',
              padding: '10px',
              borderRadius: '4px',
              margin: '5px 0',
              fontFamily: 'monospace'
            }}>
              {error}
            </div>
          ))}
        </div>
      )}

      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        Verifique o console do navegador para mais detalhes
      </div>
    </div>
  );
};

export default DiagnosticApp;