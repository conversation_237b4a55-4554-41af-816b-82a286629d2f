/// <reference types="vite/client" />

interface ImportMeta {
  env: {
    [key: string]: string | boolean | undefined;
    VITE_FIREBASE_API_KEY: string;
    VITE_FIREBASE_AUTH_DOMAIN: string;
    VITE_FIREBASE_PROJECT_ID: string;
    VITE_FIREBASE_STORAGE_BUCKET: string;
    VITE_FIREBASE_MESSAGING_SENDER_ID: string;
    VITE_FIREBASE_APP_ID: string;
    VITE_FLOW_BASE_URL: string;
    VITE_FLOW_TENANT: string;
    VITE_FLOW_CLIENT_ID: string;
    VITE_FLOW_CLIENT_SECRET: string;
    VITE_FLOW_APP_TO_ACCESS: string;
  };
}
