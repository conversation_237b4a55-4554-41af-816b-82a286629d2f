/**
 * Tipos comuns e utilitários
 */

// Tipos de status genéricos
export type LoadingStatus = 'idle' | 'loading' | 'success' | 'error';

// Resposta de API genérica
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  status: 'success' | 'error';
  message?: string;
}

// Configurações de componente
export interface ComponentConfig {
  className?: string;
  disabled?: boolean;
  loading?: boolean;
}

// Propriedades de componente base
export interface BaseComponentProps {
  id?: string;
  className?: string;
  children?: React.ReactNode;
}