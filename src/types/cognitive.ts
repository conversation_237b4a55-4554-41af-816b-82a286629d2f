/**
 * Tipos relacionados aos perfis cognitivos
 */

export enum CognitiveType {
  DEFAULT = 'DEFAULT',
  ADHD = 'ADHD',
  AUTISM = 'AUTISM',
  DYSLEXIA = 'DYSLEXIA',
}

export type FontSize = 'text-sm' | 'text-base' | 'text-lg' | 'text-xl';

export interface CognitiveProfileInfo {
  id: CognitiveType;
  name: string;
  description: string;
  icon: string; // SVG path or emoji
  colorClass: string; // Tailwind color class
}

export interface VideoSegment {
  segment: string; // e.g., "0:00-1:30"
  topic: string;
}

export interface VideoAnalysisResult {
  title: string;
  summary: string;
  concepts: string[];
  structure: VideoSegment[];
  adaptationSuggestions: string[];
}

export interface CognitiveProfileConfig {
  ui: {
    maxElementsPerScreen?: number;
    colorScheme?: string;
    animationLevel?: string;
    fontSize?: string; // e.g., 'large', '18px'
    buttonMinSize?: number; // px
    layoutConsistent?: boolean;
    navigationAlwaysVisible?: boolean;
    changeWarnings?: boolean;
    fontFamily?: string; // e.g., 'OpenDyslexic'
    lineHeight?: number;
    characterSpacing?: number;
    wordSpacing?: number;
  };
  interaction: {
    maxSessionDuration?: number; // minutes
    breakReminders?: boolean;
    autoSave?: boolean;
    confirmationDialogs?: boolean; // For app UI elements
    literalLanguage?: boolean; // For AI assistant's language
    noIdiomsSlang?: boolean; // For AI assistant
    confirmationRequired?: boolean; // AI should ask for confirmation
    undoAlwaysAvailable?: boolean; // App feature
    unlimitedTime?: boolean; // For tasks
    audioSupport?: boolean; // Suggest TTS
    speechRecognition?: boolean; // Future app feature
    textToSpeech?: boolean; // App has TTS
    readingSpeed?: string; // For TTS
    spellingAssistance?: boolean; // Future app feature
    assistantStyle?: string; // Detailed instructions for LLM personality/style
  };
  content: { // Primarily for content generation from video, but some can apply to chat
    chunkSize?: string; // 'small', 'medium', 'large' for text blocks
    progressVisible?: boolean;
    completionRewards?: boolean; // AI can give textual rewards
    difficultyAdaptive?: boolean;
    structureVisible?: boolean; // AI can outline steps
    routinesSupported?: boolean;
    surprisesMinimal?: boolean;
    sensoryOverloadPrevention?: boolean; // AI can be mindful of this
    textMinimal?: boolean;
    visualsRich?: boolean; // AI can describe visuals
    audioAlternatives?: boolean; // AI can suggest audio
    summariesProvided?: boolean; // AI can summarize
    keywordsHighlighted?: boolean; // AI can use markdown for emphasis
  };
}