/**
 * Tipos relacionados à autenticação
 */

// Usuário autenticado
export interface AuthUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
}

// Contexto de autenticação
export interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<AuthUser>;
  signOut: () => Promise<void>;
}

// Consentimentos do usuário (versão consolidada)
export interface UserConsent {
  allowVideoAnalysis: boolean;
  allowContentGeneration: boolean;
  allowAIChatInteraction: boolean;
  allowWellnessTracking: boolean;
  allowTemporaryStorage: boolean;
  allowAnalytics: boolean;
  dataRetentionDays: 1 | 7 | 30; // Valores específicos permitidos
  autoDeleteEnabled: boolean;
  consentVersion: string;
  consentDate: string; // ISO Date string
}
