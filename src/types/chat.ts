/**
 * Tipos relacionados ao sistema de chat
 */

// Role unificado para mensagens de chat (UI)
export type ChatMessageRole = 'user' | 'assistant' | 'system';

// Role para APIs de LLM (mais específico)
export type LLMMessageRole = 'system' | 'user' | 'assistant' | 'function' | 'tool';

export interface ChatMessageArtifact {
  type: 'image' | 'html';
  status: 'idle' | 'loading' | 'loaded' | 'error';
  dataUrl?: string; // base64 data URL for image
  htmlContent?: string; // HTML string for interactive content
  promptUsed?: string; // For image prompt or main idea for HTML
  error?: string;
}

// Mensagem de chat para UI (versão principal)
export interface ChatMessage {
  id: string;
  role: ChatMessageRole;
  text: string;
  timestamp: Date;
  artifact?: ChatMessageArtifact;
}

// Mensagem de chat para APIs de LLM
export interface LLMMessage {
  role: LLMMessageRole;
  content: string;
  name?: string;
}