/**
 * Tipos relacionados ao sistema de temas
 */

export type ThemeMode = 'light' | 'dark' | 'system';

export type CognitivePersona = 'DEFAULT' | 'ADHD' | 'AUTISM' | 'DYSLEXIA';

export interface ThemeColors {
  // Cores Primárias
  primary: string;
  primaryHover: string;
  primaryLight: string;
  primaryDark: string;
  
  // Cores Secundárias
  secondary: string;
  secondaryHover: string;
  secondaryLight: string;
  
  // Cores de Destaque
  accent: string;
  accentHover: string;
  accentLight: string;
  
  // Estados de Feedback
  success: string;
  warning: string;
  error: string;
  info: string;
  
  // Backgrounds
  backgroundPrimary: string;
  backgroundSecondary: string;
  backgroundTertiary: string;
  backgroundQuaternary: string;
  
  // Textos
  textPrimary: string;
  textSecondary: string;
  textTertiary: string;
  textMuted: string;
  textInverse: string;
  
  // Bordas
  borderColor: string;
  borderColorLight: string;
  borderColorDark: string;
  borderFocus: string;
}

export interface ThemeShadows {
  sm: string;
  md: string;
  lg: string;
  xl: string;
}

export interface ThemeConfig {
  mode: ThemeMode;
  persona: CognitivePersona;
  colors: ThemeColors;
  shadows: ThemeShadows;
}

export interface ThemeContextType {
  theme: ThemeMode;
  persona: CognitivePersona;
  setTheme: (theme: ThemeMode) => void;
  setPersona: (persona: CognitivePersona) => void;
  toggleTheme: () => void;
  getThemeConfig: () => ThemeConfig;
}

// Utility types for theme classes
export type ThemeBackgroundClass = 
  | 'bg-theme-primary'
  | 'bg-theme-secondary'
  | 'bg-theme-tertiary'
  | 'bg-theme-quaternary';

export type ThemeTextClass = 
  | 'text-theme-primary'
  | 'text-theme-secondary'
  | 'text-theme-tertiary'
  | 'text-theme-muted'
  | 'text-theme-inverse';

export type ThemeBorderClass = 
  | 'border-theme'
  | 'border-theme-light'
  | 'border-theme-dark'
  | 'border-theme-focus';

export type ThemeButtonClass = 
  | 'btn-primary'
  | 'btn-secondary'
  | 'btn-accent'
  | 'btn-success'
  | 'btn-warning'
  | 'btn-error';

export type ThemeCardClass = 
  | 'card-theme'
  | 'card-theme-elevated';

export type ThemeInputClass = 
  | 'input-theme'
  | 'input-success'
  | 'input-warning'
  | 'input-error';

// Persona-specific configurations
export interface PersonaThemeConfig {
  id: CognitivePersona;
  name: string;
  description: string;
  icon: string;
  colorScheme: {
    light: Partial<ThemeColors>;
    dark: Partial<ThemeColors>;
  };
  uiPreferences: {
    fontSize?: 'sm' | 'base' | 'lg' | 'xl';
    fontFamily?: string;
    lineHeight?: number;
    letterSpacing?: number;
    wordSpacing?: number;
    borderRadius?: 'none' | 'sm' | 'md' | 'lg';
    animationLevel?: 'none' | 'reduced' | 'normal';
    buttonMinSize?: number;
    maxElementsPerScreen?: number;
  };
}

// Theme utility functions types
export type ThemeUtilityFunction = {
  getThemeClass: (type: 'background' | 'text' | 'border' | 'button' | 'card' | 'input', variant?: string) => string;
  getCSSVariable: (variable: keyof ThemeColors) => string;
  applyPersonaStyles: (persona: CognitivePersona) => void;
  validateThemeContrast: (foreground: string, background: string) => boolean;
};
