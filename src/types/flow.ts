/**
 * Tipos relacionados ao serviço Flow
 */

// Configuração do serviço Flow
export interface FlowConfig {
  flowBaseUrl: string;
  flowTenant: string;
  flowClientId: string;
  flowClientSecret: string;
  flowAppToAccess: string;
  flowAgent?: string;
  flowAuthBaseUrl?: string;
  apiModelId?: string;
  modelTemperature?: number;
}

// Informações de token de autenticação
export interface FlowToken {
  accessToken: string;
  expiresAt: number;
  tokenType: string;
}

// Modelo LLM
export interface FlowModel {
  id: string;
  name: string;
  provider: string;
  capabilities: string[];
  tokenLimit?: number;
}

// Importa tipos de chat unificados
import { LLMMessage, ChatMessage } from './chat';

// Sessão Flow armazenada
export interface FlowSession {
  id: string;
  userId: string;
  name?: string; // Nome personalizado da sessão
  createdAt: Date;
  lastUsedAt: Date;
  modelId: string;
  flowConfig: FlowConfig;
  messages: ChatMessage[];
}

// Parâmetros para criação de Chat Completion
export interface ChatCompletionParams {
  messages: LLMMessage[];
  modelId: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

// Resultado de Chat Completion
export interface ChatCompletionResult {
  content: string;
  finishReason: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}
