import React, { useState } from 'react';
import { useAuth } from '@/components/AuthProvider';
import { useTheme } from '@/contexts/ThemeContext';
import { Alert } from '@/components/ui/Alert';
import { Card } from '@/components/ui/Card';
import { ThemeToggle } from '@/components/ThemeToggle';
import { PersonaPreview } from '@/components/PersonaPreview';
import { COGNITIVE_PROFILES } from '@/constants';

const LoginPage: React.FC = () => {
  const { signInWithGoogle, loading, error } = useAuth();
  const { persona, effectiveTheme } = useTheme();
  const [localError, setLocalError] = useState<string | null>(null);

  // Buscar o nome traduzido da persona
  const currentProfile = COGNITIVE_PROFILES.find(p => p.id === persona);
  const personaName = currentProfile?.name || 'Padrão';

  const handleLogin = async () => {
    try {
      setLocalError(null);
      await signInWithGoogle();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao fazer login';
      setLocalError(errorMessage);
    }
  };

  return (
    <div className="min-h-screen bg-theme-primary">
      {/* Header fixo com indicador de persona */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-theme-secondary border-b border-theme shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className="w-8 h-8 rounded-lg flex items-center justify-center shadow-sm"
              style={{
                background: `linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-accent)))`
              }}
            >
              <span className="text-white font-bold text-sm">🧠</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-theme-primary">
                Flow AdaptEd
              </h1>
              <div className="flex items-center space-x-2 text-xs text-theme-secondary">
                <span>Perfil:</span>
                <span className="font-medium text-theme-primary">{personaName}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <PersonaPreview compact />
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Conteúdo principal centralizado */}
      <div className="min-h-screen flex items-center justify-center px-4 pt-20">
        <div className="max-w-lg w-full">
          {/* Card de login centralizado */}
          <Card className="card-theme w-full overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 animate-fadeInUp">
              {/* Header simplificado */}
              <div className="text-center p-8 pb-6">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-theme-primary to-theme-accent rounded-full flex items-center justify-center shadow-lg">
                  <svg
                    className={`w-8 h-8 ${effectiveTheme === 'dark' ? 'text-white' : 'text-gray-800'}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <h2 className="text-2xl font-bold mb-2 text-theme-primary">Bem-vindo</h2>
                <p className="text-theme-secondary">
                  Faça login para acessar a plataforma Flow AdaptEd
                </p>
              </div>

              {/* Conteúdo principal do card */}
              <div className="px-8 pb-8">

                {/* Mensagens de erro */}
                {(error || localError) && (
                  <div className="mb-6">
                    <Alert variant="error" title="Erro de autenticação">
                      {error || localError}
                    </Alert>
                  </div>
                )}

                {/* Botão do Google melhorado */}
                <button
                  onClick={handleLogin}
                  disabled={loading}
                  className="w-full h-12 bg-white hover:bg-gray-50 border border-gray-300 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center space-x-3 font-medium text-gray-700 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed mb-6"
                >
                  {!loading ? (
                    <>
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      <span>Entrar com Google</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5 animate-spin text-gray-500" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
                      </svg>
                      <span>Conectando...</span>
                    </>
                  )}
                </button>

                {/* Termos e privacidade simplificados */}
                <div className="text-center">
                  <p className="text-xs text-theme-muted">
                    Ao continuar, você concorda com nossos{' '}
                    <a href="#" className="text-theme-primary hover:underline">
                      Termos de Uso
                    </a>{' '}
                    e{' '}
                    <a href="#" className="text-theme-primary hover:underline">
                      Política de Privacidade
                    </a>
                  </p>
                </div>
              </div>
            </Card>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
