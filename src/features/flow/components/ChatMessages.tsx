import React from 'react';
import { ChatMessage } from '@/types/chat';
import { AuthUser } from '@/types/auth';
import { ChatBubble } from '@/components/ui/ChatBubble';
import { MessageContentRenderer } from './MessageContentRenderer';

interface ChatMessagesProps {
  messages: ChatMessage[];
  loadingResponse: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
  editingMessageId: string | null;
  editingMessageText: string;
  onStartEditMessage: (messageId: string, currentText: string) => void;
  onSaveEditMessage: () => Promise<boolean>;
  onCancelEditMessage: () => void;
  onDeleteMessage: (messageId: string) => Promise<boolean>;
  onEditTextChange: (text: string) => void;
  user: AuthUser | null;
}

const MemoizedChatBubble = React.memo(ChatBubble);

export const ChatMessages: React.FC<ChatMessagesProps> = ({
  messages,
  loadingResponse,
  messagesEndRef,
  editingMessageId,
  editingMessageText,
  onStartEditMessage,
  onSaveEditMessage,
  onCancelEditMessage,
  onDeleteMessage,
  onEditTextChange,
  user,
}) => {
  const renderMessageBubble = React.useCallback((message: ChatMessage) => {
    return (
      <MemoizedChatBubble
        message={message}
        isEditing={editingMessageId === message.id}
        editingText={editingMessageText}
        onStartEdit={onStartEditMessage}
        onSaveEdit={onSaveEditMessage}
        onCancelEdit={onCancelEditMessage}
        onDelete={onDeleteMessage}
        onEditTextChange={onEditTextChange}
        renderMessageContent={(text: string) => <MessageContentRenderer text={text} />}
        user={user}
      />
    );
  }, [
    editingMessageId,
    editingMessageText,
    onStartEditMessage,
    onSaveEditMessage,
    onCancelEditMessage,
    onDeleteMessage,
    onEditTextChange,
    user,
  ]);

  // Filtrar mensagens do sistema apenas na renderização
  const visibleMessages = React.useMemo(
    () => messages.filter(msg => msg.role !== 'system'),
    [messages]
  );

  return (
    <div className="max-w-4xl mx-auto space-y-2">
      {visibleMessages.map((message) => (
        <div key={message.id}>
          {renderMessageBubble(message)}
        </div>
      ))}
      
      {loadingResponse && (
        <div className="flex justify-start">
          <div className="bg-theme-secondary text-theme-primary rounded-lg px-3 py-2 max-w-[80%]">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-3 w-3 border-t-2 border-b-2 border-theme-accent"></div>
              <span className="text-xs text-theme-muted">FlowEd está pensando...</span>
            </div>
          </div>
        </div>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
};