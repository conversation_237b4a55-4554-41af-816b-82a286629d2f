import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useFlow } from '@/components/FlowProvider';
import { useModelSelector } from '../hooks/useModelSelector';
import { generateSessionDisplayName } from '../utils/messageFormatters';

interface ChatHeaderProps {
  isEditingName: boolean;
  editingName: string;
  showModelSelector: boolean;
  onStartEditingName: () => void;
  onSaveEditedName: () => void;
  onCancelEditingName: () => void;
  onEditingNameChange: (name: string) => void;
  onToggleModelSelector: (show: boolean) => void;
  onModelChange: (sessionId: string, modelId: string) => Promise<boolean>;
  loading?: boolean;
  loadingResponse?: boolean;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  isEditingName,
  editingName,
  showModelSelector,
  onStartEditingName,
  onSaveEditedName,
  onCancelEditingName,
  onEditingNameChange,
  onToggleModelSelector,
  onModelChange,
  loading = false,
  loadingResponse = false,
}) => {
  const navigate = useNavigate();
  const { currentSession } = useFlow();
  
  const {
    validModels,
    currentModelDisplayName,
    modelSelectorRef,
    handleModelChange,
    toggleSelector,
  } = useModelSelector({
    showModelSelector,
    onToggleSelector: onToggleModelSelector,
    onModelChange,
  });

  const sessionDisplayName = generateSessionDisplayName(
    currentSession?.id ?? '',
    currentSession?.name
  );

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') onSaveEditedName();
    if (e.key === 'Escape') onCancelEditingName();
  };

  return (
    <div className="bg-theme-secondary border-b border-theme px-3 py-2 shadow-sm">
      <div className="flex items-center justify-between">
        {/* Lado esquerdo - Voltar e Título */}
        <div className="flex items-center space-x-3 flex-1 min-w-0">
          <button
            onClick={() => navigate('/')}
            className="text-theme-muted hover:text-theme-primary transition-colors p-1 rounded hover:bg-theme-tertiary"
            title="Voltar"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </button>

          {isEditingName ? (
            <div className="flex items-center space-x-2 flex-1">
              <input
                type="text"
                value={editingName}
                onChange={(e) => onEditingNameChange(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1 text-sm font-medium text-theme-primary bg-theme-primary border border-theme rounded px-2 py-1 focus:ring-1 focus:ring-theme-focus focus:border-theme-focus"
                autoFocus
              />
              <button onClick={onSaveEditedName} className="text-green-600 hover:text-green-700 p-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </button>
              <button onClick={onCancelEditingName} className="text-red-600 hover:text-red-700 p-1">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <button
                className="text-sm font-medium text-theme-primary truncate hover:text-theme-accent transition-colors text-left w-full"
                onClick={onStartEditingName}
                title="Clique para editar"
              >
                {sessionDisplayName}
              </button>
              <button
                onClick={onStartEditingName}
                className="text-theme-muted hover:text-theme-primary transition-colors p-1 rounded hover:bg-theme-tertiary flex-shrink-0"
                title="Editar nome"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Lado direito - Seletor de modelo */}
        {validModels.length > 0 && (
          <div className="flex items-center space-x-2 flex-shrink-0">
            {/* Desktop: Select normal */}
            <select
              value={currentSession?.modelId ?? ''}
              onChange={(e) => handleModelChange(e.target.value)}
              className="text-xs bg-theme-primary border border-theme rounded px-2 py-1 text-theme-primary focus:ring-1 focus:ring-theme-focus focus:border-theme-focus hidden sm:block"
              disabled={loading || loadingResponse}
            >
              <option value="" disabled>Modelo...</option>
              {validModels.map((model, index) => (
                <option key={`${model.id}-${index}`} value={model.id}>
                  {model.name && model.name.length > 20
                    ? model.name.substring(0, 20) + '...'
                    : model.name ?? model.id
                  }
                </option>
              ))}
            </select>

            {/* Mobile: Botão mais visível para seleção */}
            <div className="sm:hidden relative" ref={modelSelectorRef}>
              <button
                className="flex items-center space-x-1 bg-theme-tertiary hover:bg-theme-secondary text-theme-primary px-2 py-1 rounded text-xs border border-theme transition-colors"
                title="Selecionar modelo"
                onClick={toggleSelector}
              >
                <span className="max-w-[60px] truncate">
                  {currentModelDisplayName}
                </span>
                <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {/* Dropdown de modelos */}
              {showModelSelector && (
                <div className="absolute right-0 top-full mt-1 bg-theme-primary border border-theme rounded-lg shadow-lg z-50 min-w-[200px] max-h-[200px] overflow-y-auto">
                  {validModels.map((model, index) => {
                    const isSelected = model.id === currentSession?.modelId;
                    return (
                      <button
                        key={`mobile-${model.id}-${index}`}
                        className={`w-full text-left px-3 py-2 text-xs hover:bg-theme-secondary transition-colors ${
                          isSelected ? 'bg-theme-accent text-white' : 'text-theme-primary'
                        }`}
                        onClick={() => handleModelChange(model.id)}
                      >
                        <div className="font-medium">{model.name ?? model.id}</div>
                        {model.provider && (
                          <div className="text-xs opacity-70">{model.provider}</div>
                        )}
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};