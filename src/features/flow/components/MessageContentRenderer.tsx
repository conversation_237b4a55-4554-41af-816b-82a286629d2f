import React from 'react';
import { detectMessageContentType, formatCodeContent } from '../utils/messageFormatters';

interface MessageContentRendererProps {
  text: string;
}

export const MessageContentRenderer: React.FC<MessageContentRendererProps> = ({ text }) => {
  const contentType = detectMessageContentType(text);

  // Renderizar código
  if (contentType === 'code') {
    const parts = text.split(/(```[\s\S]*?```)/);
    return (
      <>
        {parts.map((part, index) => {
          if (part.startsWith('```') && part.endsWith('```')) {
            const { language, codeContent } = formatCodeContent(part);
            
            return (
              <div key={`code-${index}-${part.slice(0, 20)}`} className="my-2 bg-gray-900 text-green-400 rounded p-3 font-mono text-sm overflow-x-auto">
                {language && (
                  <div className="text-xs text-gray-400 mb-2 border-b border-gray-700 pb-1">
                    {language}
                  </div>
                )}
                <pre className="whitespace-pre-wrap">{codeContent}</pre>
              </div>
            );
          }
          return <span key={`text-${index}-${part.slice(0, 20)}`}>{part}</span>;
        })}
      </>
    );
  }

  // Detectar solicitações de imagem
  if (contentType === 'image-request') {
    return (
      <div>
        {text}
        <div className="mt-2 p-3 bg-blue-100 dark:bg-blue-900 rounded-lg border border-blue-300 dark:border-blue-700">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">🎨</span>
            <div>
              <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Geração de Imagens
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-300">
                Esta funcionalidade será implementada em breve. Por enquanto, posso ajudar com descrições detalhadas.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Detectar solicitações de artefatos interativos
  if (contentType === 'interactive-request') {
    return (
      <div>
        {text}
        <div className="mt-2 p-3 bg-purple-100 dark:bg-purple-900 rounded-lg border border-purple-300 dark:border-purple-700">
          <div className="flex items-center space-x-2">
            <span className="text-2xl">⚡</span>
            <div>
              <p className="text-sm font-medium text-purple-800 dark:text-purple-200">
                Artefatos Interativos
              </p>
              <p className="text-xs text-purple-600 dark:text-purple-300">
                Em breve poderei criar componentes interativos. Por enquanto, posso fornecer o código completo.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Texto simples
  return <>{text}</>;
};