import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { CognitiveType } from '@/types/cognitive';
import { generatePersonaIcon, generatePersonaName } from '../utils/messageFormatters';
import { useModelSelector } from '../hooks/useModelSelector';

interface WelcomeScreenProps {
  showModelSelector: boolean;
  onToggleModelSelector: (show: boolean) => void;
  onModelChange: (sessionId: string, modelId: string) => Promise<boolean>;
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({
  showModelSelector,
  onToggleModelSelector,
  onModelChange,
}) => {
  const { persona } = useTheme();
  
  const {
    validModels,
    currentModelDisplayName,
  } = useModelSelector({
    showModelSelector,
    onToggleSelector: onToggleModelSelector,
    onModelChange,
  });

  const personaIcon = generatePersonaIcon(persona);
  const personaName = generatePersonaName(persona);

  return (
    <div className="text-center py-8">
      <div className="text-4xl mb-3">{personaIcon}</div>
      <h2 className="text-lg font-semibold text-theme-primary">Olá! Sou o FlowEd 👋</h2>
      <p className="text-sm text-theme-secondary mt-2 max-w-sm mx-auto">
        Seu assistente cognitivo personalizado. Como posso ajudar?
      </p>
      
      {persona !== CognitiveType.DEFAULT && (
        <div className="mt-3 p-3 bg-theme-secondary rounded-lg max-w-sm mx-auto">
          <p className="text-xs text-theme-primary">
            ✨ <strong>Modo:</strong> {personaName}
          </p>
        </div>
      )}

      {/* Seletor de modelo na tela de boas-vindas (mobile) */}
      {validModels.length > 0 && (
        <div className="mt-4 sm:hidden">
          <p className="text-xs text-theme-muted mb-2">Modelo atual:</p>
          <button
            className="bg-theme-secondary hover:bg-theme-tertiary text-theme-primary px-3 py-2 rounded-lg text-sm border border-theme transition-colors flex items-center space-x-2 mx-auto"
            onClick={() => onToggleModelSelector(!showModelSelector)}
          >
            <span>{currentModelDisplayName}</span>
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      )}
    </div>
  );
};