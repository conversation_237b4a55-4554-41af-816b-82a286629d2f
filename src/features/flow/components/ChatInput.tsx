import React, { useRef, useEffect } from 'react';
import { Button } from '@/components/ui/Button';

interface ChatInputProps {
  inputValue: string;
  onInputChange: (value: string) => void;
  onSendMessage: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  placeholder?: string;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  onInputChange,
  onSendMessage,
  isLoading = false,
  disabled = false,
  placeholder = "Digite sua mensagem...",
}) => {
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Foco automático no input
  useEffect(() => {
    if (!isLoading && !disabled && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isLoading, disabled]);

  // Lidar com tecla Enter
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSendMessage();
    }
  };

  return (
    <div className="border-t border-theme bg-theme-secondary p-3">
      <div className="max-w-4xl mx-auto">
        <div className="bg-theme-primary border border-theme rounded-lg shadow-sm">
          <div className="flex items-end space-x-2 p-3">
            <textarea
              ref={inputRef}
              className="flex-1 resize-none border-0 bg-transparent py-1 px-0 focus:ring-0 focus:outline-none min-h-[36px] max-h-[120px] text-sm text-theme-primary placeholder-theme-muted"
              placeholder={placeholder}
              value={inputValue}
              onChange={(e) => onInputChange(e.target.value)}
              onKeyDown={handleKeyDown}
              disabled={disabled}
            />
            <Button
              onClick={onSendMessage}
              isLoading={isLoading}
              disabled={!inputValue.trim() || isLoading || disabled}
              size="sm"
              className="flex items-center space-x-1 px-3"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
              <span className="hidden sm:inline">Enviar</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};