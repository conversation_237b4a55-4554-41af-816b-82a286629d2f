import { CognitiveType } from '@/types/cognitive';
import { CHAT_CONSTANTS } from '../constants/chat';

// Sanitizar entrada de texto (placeholder para implementação futura com DOMPurify)
export const sanitizeMessage = (text: string): string => {
  // Por enquanto, apenas trim e validação básica
  // TODO: Implementar DOMPurify quando necessário
  return text.trim();
};

// Validar tamanho da mensagem
export const validateMessageLength = (text: string): { isValid: boolean; error?: string } => {
  if (text.length > CHAT_CONSTANTS.MESSAGE_MAX_LENGTH) {
    return {
      isValid: false,
      error: `Mensagem muito longa. Máximo ${CHAT_CONSTANTS.MESSAGE_MAX_LENGTH} caracteres.`
    };
  }
  return { isValid: true };
};

// Gerar ícone da persona
export const generatePersonaIcon = (persona: CognitiveType): string => {
  switch (persona) {
    case CognitiveType.ADHD:
      return '🎯';
    case CognitiveType.AUTISM:
      return '🧩';
    case CognitiveType.DYSLEXIA:
      return '📖';
    default:
      return '🤖';
  }
};

// Gerar nome da persona para exibição
export const generatePersonaName = (persona: CognitiveType): string => {
  switch (persona) {
    case CognitiveType.ADHD:
      return 'TDAH Foco';
    case CognitiveType.AUTISM:
      return 'Autismo Estruturado';
    case CognitiveType.DYSLEXIA:
      return 'Dislexia Legível';
    default:
      return 'Padrão';
  }
};

// Gerar nome da sessão baseado no ID
export const generateSessionDisplayName = (sessionId: string, sessionName?: string): string => {
  if (sessionName) return sessionName;
  return `Chat ${sessionId.substring(0, CHAT_CONSTANTS.CHAT_ID_DISPLAY_LENGTH)}...`;
};

// Truncar texto com ellipsis
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// Detectar tipo de conteúdo da mensagem
export const detectMessageContentType = (text: string): 'code' | 'image-request' | 'interactive-request' | 'text' => {
  const lowerText = text.toLowerCase();
  
  if (text.includes('```')) {
    return 'code';
  }
  
  if (lowerText.includes('gere uma imagem') || lowerText.includes('criar imagem')) {
    return 'image-request';
  }
  
  if (lowerText.includes('criar um') && 
      (lowerText.includes('formulário') || lowerText.includes('calculadora') || lowerText.includes('jogo'))) {
    return 'interactive-request';
  }
  
  return 'text';
};

// Formatar conteúdo de código
export const formatCodeContent = (codeBlock: string) => {
  const code = codeBlock.slice(3, -3).trim();
  const lines = code.split('\n');
  const language = lines[0] || '';
  const codeContent = lines.length > 1 ? lines.slice(1).join('\n') : code;
  
  return { language, codeContent };
};