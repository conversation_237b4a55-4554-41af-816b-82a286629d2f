export type ChatState = 'idle' | 'creating-session' | 'sending-message' | 'loading-history' | 'error';
export type MessageAction = 'copy' | 'edit' | 'delete';

export interface LoadingStates {
  creatingSession: boolean;
  sendingMessage: boolean;
  loadingHistory: boolean;
  changingModel: boolean;
}

export interface ChatUIState {
  isEditingName: boolean;
  editingName: string;
  editingMessageId: string | null;
  editingMessageText: string;
  showModelSelector: boolean;
  loadingStates: LoadingStates;
  error: string | null;
}

export interface ChatUIActions {
  setIsEditingName: (editing: boolean) => void;
  setEditingName: (name: string) => void;
  setEditingMessageId: (id: string | null) => void;
  setEditingMessageText: (text: string) => void;
  setShowModelSelector: (show: boolean) => void;
  setLoadingStates: (states: Partial<LoadingStates>) => void;
  setError: (error: string | null) => void;
  resetEditingState: () => void;
}

export type ChatUIAction =
  | { type: 'SET_EDITING_NAME'; payload: boolean }
  | { type: 'SET_NAME_TEXT'; payload: string }
  | { type: 'SET_EDITING_MESSAGE_ID'; payload: string | null }
  | { type: 'SET_EDITING_MESSAGE_TEXT'; payload: string }
  | { type: 'SET_SHOW_MODEL_SELECTOR'; payload: boolean }
  | { type: 'SET_LOADING_STATES'; payload: Partial<LoadingStates> }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET_EDITING_STATE' };