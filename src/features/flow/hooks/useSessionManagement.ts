import { useState, useCallback } from 'react';
import { useFlow } from '@/components/FlowProvider';
import { CHAT_CONSTANTS } from '../constants/chat';

interface UseSessionManagementProps {
  sessionId?: string;
  onError: (error: string) => void;
  onLoadingChange: (loading: boolean) => void;
}

export function useSessionManagement({ 
  sessionId, 
  onError, 
  onLoadingChange 
}: UseSessionManagementProps) {
  const {
    isConfigured,
    loadSession,
    createSession,
    currentSession,
    renameSession,
    changeSessionModel
  } = useFlow();

  const [isInitialized, setIsInitialized] = useState(false);

  // Inicializar sessão
  const initializeSession = useCallback(async () => {
    if (!isConfigured) {
      onError("Flow não está configurado. Configure o Flow antes de usar o chat.");
      return false;
    }

    try {
      onLoadingChange(true);
      onError('');

      if (sessionId) {
        // Carregar sessão existente se ainda não foi carregada
        if (!currentSession || currentSession.id !== sessionId) {
          console.log("📂 [useSessionManagement] Carregando sessão:", sessionId);
          const session = await loadSession(sessionId);
          if (!session) {
            onError(`Sessão ${sessionId} não encontrada`);
            return false;
          }
        }
      } else if (!currentSession) {
        // Criar nova sessão apenas se não temos uma sessão atual
        console.log("🆕 [useSessionManagement] Criando nova sessão");
        await createSession();
      }

      setIsInitialized(true);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao inicializar chat';
      console.error("❌ [useSessionManagement] Erro:", err);
      onError(errorMessage);
      return false;
    } finally {
      onLoadingChange(false);
    }
  }, [sessionId, isConfigured, loadSession, createSession, currentSession, onError, onLoadingChange]);

  // Criar sessão para primeira mensagem
  const ensureSession = useCallback(async () => {
    if (currentSession) return currentSession;

    console.log("🆕 [useSessionManagement] Criando sessão para primeira mensagem");
    const newSession = await createSession();
    console.log("✅ [useSessionManagement] Sessão criada:", newSession?.id);

    // Aguardar até que a sessão esteja realmente disponível no estado
    let retries = 0;
    while (!currentSession && retries < CHAT_CONSTANTS.SESSION_RETRY_ATTEMPTS) {
      await new Promise(resolve => setTimeout(resolve, CHAT_CONSTANTS.SESSION_RETRY_DELAY));
      retries++;
      console.log(`⏳ [useSessionManagement] Aguardando sessão no estado (tentativa ${retries})`);
    }

    if (!currentSession) {
      console.warn("⚠️ [useSessionManagement] Sessão não disponível no estado após criação");
    }

    return newSession || currentSession;
  }, [currentSession, createSession]);

  // Renomear sessão com debounce
  const handleRenameSession = useCallback(async (sessionId: string, newName: string) => {
    if (!newName.trim()) return false;

    try {
      await renameSession(sessionId, newName.trim());
      return true;
    } catch (err) {
      console.error('❌ [useSessionManagement] Erro ao renomear sessão:', err);
      onError('Erro ao renomear sessão');
      return false;
    }
  }, [renameSession, onError]);

  // Trocar modelo da sessão
  const handleModelChange = useCallback(async (sessionId: string, newModelId: string) => {
    console.log("🔄 [useSessionManagement] Trocando modelo", {
      sessionId,
      newModel: newModelId
    });

    try {
      await changeSessionModel(sessionId, newModelId);
      console.log("✅ [useSessionManagement] Modelo trocado com sucesso");
      return true;
    } catch (err) {
      console.error('❌ [useSessionManagement] Erro ao trocar modelo:', err);
      onError('Erro ao trocar modelo');
      return false;
    }
  }, [changeSessionModel, onError]);

  return {
    isInitialized,
    initializeSession,
    ensureSession,
    handleRenameSession,
    handleModelChange,
  };
}