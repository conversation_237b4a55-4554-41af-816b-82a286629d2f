import { useCallback, useRef, useEffect } from 'react';
import { useFlow } from '@/components/FlowProvider';
import { CHAT_CONSTANTS } from '../constants/chat';

interface UseChatMessagesProps {
  onError: (error: string) => void;
  ensureSession: () => Promise<unknown>;
}

export function useChatMessages({ onError, ensureSession }: UseChatMessagesProps) {
  const {
    messages,
    sendMessage,
    loadingResponse,
    editMessage,
    deleteMessage,
    currentSession
  } = useFlow();

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll automático para última mensagem
  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: CHAT_CONSTANTS.SCROLL_BEHAVIOR 
      });
    }
  }, []);

  // Effect para scroll automático quando mensagens mudam
  useEffect(() => {
    console.log("📜 [useChatMessages] Mensagens mudaram:", {
      count: messages.length,
      messages: messages.map(m => ({ 
        id: m.id, 
        role: m.role, 
        text: m.text.substring(0, 50) + '...' 
      }))
    });
    
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Enviar mensagem
  const handleSendMessage = useCallback(async (messageText: string) => {
    if (!messageText.trim()) return false;

    // Validar tamanho da mensagem
    if (messageText.length > CHAT_CONSTANTS.MESSAGE_MAX_LENGTH) {
      onError(`Mensagem muito longa. Máximo ${CHAT_CONSTANTS.MESSAGE_MAX_LENGTH} caracteres.`);
      return false;
    }

    console.log("📤 [useChatMessages] Enviando mensagem", {
      currentSession: !!currentSession,
      messagesCount: messages.length,
      messageText: messageText.trim()
    });

    try {
      onError('');

      // Garantir que temos uma sessão
      let sessionToUse = currentSession;
      sessionToUse ??= await ensureSession();

      // Enviar mensagem
      console.log("📨 [useChatMessages] Enviando mensagem para sessão", {
        sessionId: sessionToUse?.id,
        messagesBeforeSend: messages.length
      });
      
      await sendMessage(messageText);
      console.log("✅ [useChatMessages] Mensagem enviada");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao enviar mensagem';
      console.error("❌ [useChatMessages] Erro ao enviar:", err);
      onError(errorMessage);
      return false;
    }
  }, [currentSession, messages.length, sendMessage, ensureSession, onError]);

  // Editar mensagem
  const handleEditMessage = useCallback(async (messageId: string, newText: string) => {
    if (!newText.trim()) return false;

    if (newText.length > CHAT_CONSTANTS.MESSAGE_MAX_LENGTH) {
      onError(`Mensagem muito longa. Máximo ${CHAT_CONSTANTS.MESSAGE_MAX_LENGTH} caracteres.`);
      return false;
    }

    try {
      await editMessage(messageId, newText.trim());
      return true;
    } catch (err) {
      console.error('❌ [useChatMessages] Erro ao editar mensagem:', err);
      onError('Erro ao editar mensagem');
      return false;
    }
  }, [editMessage, onError]);

  // Deletar mensagem
  const handleDeleteMessage = useCallback(async (messageId: string) => {
    const confirmed = window.confirm(
      'Tem certeza que deseja excluir esta mensagem? Todas as mensagens posteriores também serão removidas.'
    );
    
    if (!confirmed) return false;

    try {
      await deleteMessage(messageId);
      return true;
    } catch (err) {
      console.error('❌ [useChatMessages] Erro ao deletar mensagem:', err);
      onError('Erro ao deletar mensagem');
      return false;
    }
  }, [deleteMessage, onError]);

  return {
    messages,
    loadingResponse,
    messagesEndRef,
    handleSendMessage,
    handleEditMessage,
    handleDeleteMessage,
    scrollToBottom,
  };
}