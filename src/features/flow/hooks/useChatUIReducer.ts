import { useReducer, useMemo } from 'react';
import { Chat<PERSON><PERSON>tate, Cha<PERSON><PERSON>Action, LoadingStates } from '../types/chat-ui';

const initialLoadingStates: LoadingStates = {
  creatingSession: false,
  sendingMessage: false,
  loadingHistory: false,
  changingModel: false,
};

const initialState: ChatUIState = {
  isEditingName: false,
  editingName: '',
  editingMessageId: null,
  editingMessageText: '',
  showModelSelector: false,
  loadingStates: initialLoadingStates,
  error: null,
};

function chatUIReducer(state: ChatUIState, action: ChatUIAction): ChatUIState {
  switch (action.type) {
    case 'SET_EDITING_NAME':
      return { ...state, isEditingName: action.payload };
    
    case 'SET_NAME_TEXT':
      return { ...state, editingName: action.payload };
    
    case 'SET_EDITING_MESSAGE_ID':
      return { ...state, editingMessageId: action.payload };
    
    case 'SET_EDITING_MESSAGE_TEXT':
      return { ...state, editingMessageText: action.payload };
    
    case 'SET_SHOW_MODEL_SELECTOR':
      return { ...state, showModelSelector: action.payload };
    
    case 'SET_LOADING_STATES':
      return {
        ...state,
        loadingStates: { ...state.loadingStates, ...action.payload }
      };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'RESET_EDITING_STATE':
      return {
        ...state,
        isEditingName: false,
        editingName: '',
        editingMessageId: null,
        editingMessageText: '',
      };
    
    default:
      return state;
  }
}

export function useChatUIReducer() {
  const [state, dispatch] = useReducer(chatUIReducer, initialState);

  const actions = useMemo(() => ({
    setIsEditingName: (editing: boolean) =>
      dispatch({ type: 'SET_EDITING_NAME', payload: editing }),

    setEditingName: (name: string) =>
      dispatch({ type: 'SET_NAME_TEXT', payload: name }),

    setEditingMessageId: (id: string | null) =>
      dispatch({ type: 'SET_EDITING_MESSAGE_ID', payload: id }),

    setEditingMessageText: (text: string) =>
      dispatch({ type: 'SET_EDITING_MESSAGE_TEXT', payload: text }),

    setShowModelSelector: (show: boolean) =>
      dispatch({ type: 'SET_SHOW_MODEL_SELECTOR', payload: show }),

    setLoadingStates: (states: Partial<LoadingStates>) =>
      dispatch({ type: 'SET_LOADING_STATES', payload: states }),

    setError: (error: string | null) =>
      dispatch({ type: 'SET_ERROR', payload: error }),

    resetEditingState: () =>
      dispatch({ type: 'RESET_EDITING_STATE' }),
  }), []);

  return { state, actions };
}