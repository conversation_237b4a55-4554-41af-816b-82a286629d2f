import { useMemo, useCallback, useRef, useEffect } from 'react';
import { useFlow } from '@/components/FlowProvider';
import { CHAT_CONSTANTS } from '../constants/chat';

interface UseModelSelectorProps {
  showModelSelector: boolean;
  onToggleSelector: (show: boolean) => void;
  onModelChange: (sessionId: string, modelId: string) => Promise<boolean>;
}

export function useModelSelector({ 
  showModelSelector, 
  onToggleSelector, 
  onModelChange 
}: UseModelSelectorProps) {
  const { models, currentSession, selectedModelId } = useFlow();
  const modelSelectorRef = useRef<HTMLDivElement>(null);

  // Filtrar modelos válidos
  const validModels = useMemo(() => {
    return models.filter(model => model.id && model.id !== 'undefined');
  }, [models]);

  // Modelo atual
  const currentModel = useMemo(() => {
    const currentModelId = currentSession?.modelId ?? selectedModelId;
    return validModels.find(m => m.id === currentModelId);
  }, [validModels, currentSession?.modelId, selectedModelId]);

  // Opções para o select
  const modelOptions = useMemo(() => {
    return validModels.map(model => ({
      value: model.id,
      label: model.name ?? model.id,
      provider: model.provider,
      displayName: model.name && model.name.length > CHAT_CONSTANTS.MODEL_NAME_MAX_LENGTH 
        ? model.name.substring(0, CHAT_CONSTANTS.MODEL_NAME_MAX_LENGTH) + '...' 
        : model.name ?? model.id
    }));
  }, [validModels]);

  // Fechar dropdown quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modelSelectorRef.current && !modelSelectorRef.current.contains(event.target as Node)) {
        onToggleSelector(false);
      }
    };

    if (showModelSelector) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showModelSelector, onToggleSelector]);

  // Trocar modelo
  const handleModelChange = useCallback(async (newModelId: string) => {
    if (!currentSession || !newModelId) return false;

    console.log("🔄 [useModelSelector] Trocando modelo", {
      currentSessionId: currentSession.id,
      oldModel: currentSession.modelId ?? selectedModelId,
      newModel: newModelId,
    });

    const success = await onModelChange(currentSession.id, newModelId);
    if (success) {
      onToggleSelector(false);
    }
    return success;
  }, [currentSession, selectedModelId, onModelChange, onToggleSelector]);

  // Toggle do seletor
  const toggleSelector = useCallback(() => {
    onToggleSelector(!showModelSelector);
  }, [showModelSelector, onToggleSelector]);

  // Nome do modelo atual para exibição
  const currentModelDisplayName = useMemo(() => {
    if (!currentModel) return 'Selecionar modelo';
    
    const name = currentModel.name ?? currentModel.id;
    return name.length > CHAT_CONSTANTS.MODEL_NAME_MAX_LENGTH 
      ? name.substring(0, CHAT_CONSTANTS.MODEL_NAME_MAX_LENGTH) + '...' 
      : name;
  }, [currentModel]);

  return {
    validModels,
    currentModel,
    modelOptions,
    currentModelDisplayName,
    modelSelectorRef,
    handleModelChange,
    toggleSelector,
  };
}