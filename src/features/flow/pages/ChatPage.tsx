import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useFlow } from '@/components/FlowProvider';
import { useAuth } from '@/components/AuthProvider';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';

// Hooks customizados
import { useChatUIReducer } from '../hooks/useChatUIReducer';
import { useSessionManagement } from '../hooks/useSessionManagement';
import { useChatMessages } from '../hooks/useChatMessages';

// Componentes modularizados
import { ChatHeader } from '../components/ChatHeader';
import { WelcomeScreen } from '../components/WelcomeScreen';
import { ChatMessages } from '../components/ChatMessages';
import { ChatInput } from '../components/ChatInput';

const ChatPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isConfigured, error: flowError, currentSession } = useFlow();
  
  // Estados locais
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);

  // UI State com reducer
  const { state: uiState, actions: uiActions } = useChatUIReducer();

  // Session Management
  const {
    isInitialized,
    initializeSession,
    ensureSession,
    handleRenameSession,
    handleModelChange,
  } = useSessionManagement({
    sessionId,
    onError: uiActions.setError,
    onLoadingChange: setLoading,
  });

  // Chat Messages
  const {
    messages,
    loadingResponse,
    messagesEndRef,
    handleSendMessage,
    handleEditMessage,
    handleDeleteMessage,
  } = useChatMessages({
    onError: uiActions.setError,
    ensureSession,
  });

  // Inicializar chat quando componente carrega
  useEffect(() => {
    console.log("🔧 [ChatPage] Inicializando chat:", {
      isConfigured,
      loading,
      sessionId,
      hasCurrentSession: !!currentSession,
      isInitialized
    });

    // Aguardar carregamento inicial
    if (loading) {
      console.log("⏳ [ChatPage] Aguardando carregamento...");
      return;
    }

    // Se não está configurado, mostrar erro
    if (!isConfigured) {
      console.log("⚠️ [ChatPage] Flow não configurado");
      uiActions.setError("Flow não está configurado. Configure o Flow antes de usar o chat.");
      return;
    }

    // Só inicializar se não foi inicializado ainda
    if (!isInitialized && isConfigured && !loading) {
      console.log("🚀 [ChatPage] Executando inicialização...");
      initializeSession();
    }
  }, [sessionId, isConfigured, loading, isInitialized, initializeSession, currentSession, uiActions]); // Dependências completas

  // Handlers do Header
  const handleStartEditingName = () => {
    const sessionName = currentSession?.name ?? `Chat ${currentSession?.id.substring(0, 8)}...`;
    uiActions.setEditingName(sessionName);
    uiActions.setIsEditingName(true);
  };

  const handleSaveEditedName = async () => {
    if (!currentSession || !uiState.editingName.trim()) return;

    const success = await handleRenameSession(currentSession.id, uiState.editingName);
    if (success) {
      uiActions.setIsEditingName(false);
    }
  };

  const handleCancelEditingName = () => {
    uiActions.setIsEditingName(false);
    uiActions.setEditingName('');
  };

  // Handlers das mensagens
  const handleStartEditMessage = (messageId: string, currentText: string) => {
    uiActions.setEditingMessageId(messageId);
    uiActions.setEditingMessageText(currentText);
  };

  const handleSaveEditMessage = async (): Promise<boolean> => {
    if (!uiState.editingMessageId || !uiState.editingMessageText.trim()) return false;

    const success = await handleEditMessage(uiState.editingMessageId, uiState.editingMessageText);
    if (success) {
      uiActions.setEditingMessageId(null);
      uiActions.setEditingMessageText('');
    }
    return success;
  };

  const handleCancelEditMessage = () => {
    uiActions.setEditingMessageId(null);
    uiActions.setEditingMessageText('');
  };

  // Handler do input
  const handleSendMessageClick = async () => {
    if (!inputValue.trim()) return;

    const success = await handleSendMessage(inputValue.trim());
    if (success) {
      setInputValue('');
    }
  };

  // Renderização do conteúdo do chat (ternários separados)
  const renderChatContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-theme-focus"></div>
        </div>
      );
    }

    if (!isConfigured) {
      return (
        <div className="text-center py-8">
          <div className="text-4xl mb-3">⚙️</div>
          <h2 className="text-lg font-semibold text-theme-primary mb-2">Configuração Necessária</h2>
          <p className="text-sm text-theme-secondary mb-4 max-w-sm mx-auto">
            Configure o Flow para começar a usar o chat.
          </p>
          <Button size="sm" onClick={() => navigate('/config')}>
            Configurar Flow
          </Button>
        </div>
      );
    }

    const errorMessage = uiState.error ?? flowError;
    if (errorMessage) {
      return (
        <Alert
          variant="error"
          className="mx-auto max-w-lg"
          title="Erro"
        >
          {errorMessage}
        </Alert>
      );
    }

    if (messages.length === 0) {
      return (
        <WelcomeScreen
          showModelSelector={uiState.showModelSelector}
          onToggleModelSelector={uiActions.setShowModelSelector}
          onModelChange={handleModelChange}
        />
      );
    }

    return (
      <ChatMessages
        messages={messages}
        loadingResponse={loadingResponse}
        messagesEndRef={messagesEndRef}
        editingMessageId={uiState.editingMessageId}
        editingMessageText={uiState.editingMessageText}
        onStartEditMessage={handleStartEditMessage}
        onSaveEditMessage={handleSaveEditMessage}
        onCancelEditMessage={handleCancelEditMessage}
        onDeleteMessage={handleDeleteMessage}
        onEditTextChange={uiActions.setEditingMessageText}
        user={user}
      />
    );
  };

  return (
    <div className="chat-container bg-theme-primary">
      {/* Header */}
      <ChatHeader
        isEditingName={uiState.isEditingName}
        editingName={uiState.editingName}
        showModelSelector={uiState.showModelSelector}
        onStartEditingName={handleStartEditingName}
        onSaveEditedName={handleSaveEditedName}
        onCancelEditingName={handleCancelEditingName}
        onEditingNameChange={uiActions.setEditingName}
        onToggleModelSelector={uiActions.setShowModelSelector}
        onModelChange={handleModelChange}
        loading={loading}
        loadingResponse={loadingResponse}
      />

      {/* Área de mensagens */}
      <div className="chat-messages px-3 py-3">
        {renderChatContent()}
      </div>

      {/* Área de input */}
      <ChatInput
        inputValue={inputValue}
        onInputChange={setInputValue}
        onSendMessage={handleSendMessageClick}
        isLoading={loadingResponse}
        disabled={loadingResponse || loading || !isConfigured}
      />
    </div>
  );
};

export default ChatPage;
