import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/components/AuthProvider';
import { useFlow } from '@/components/FlowProvider';
import { useTheme } from '@/contexts/ThemeContext';
import { FlowSession } from '@/types/flow';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Alert } from '@/components/ui/Alert';
import { Header } from '@/components/Header';
import { FlowConfigModal } from '@/components/FlowConfigModal';
import { flowStorageService } from '@/services/flow/flowStorageAdapter';

const HomePage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { persona } = useTheme();
  const {
    isConfigured,
    isConfigurationComplete,
    loadUserSessions,
    createSession,
    showConfigModal,
    setShowConfigModal,
    renameSession,
    error: flowError
  } = useFlow();
  
  const [recentSessions, setRecentSessions] = useState<FlowSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');

  // Carregar sessões recentes ao inicializar a página
  useEffect(() => {
    if (isConfigured) {
      loadUserRecentSessions();
    }
  }, [isConfigured]);

  // Mostrar modal de configuração automaticamente apenas se campos obrigatórios estiverem faltando
  useEffect(() => {
    const checkConfiguration = async () => {
      // Só verifica se o usuário está logado E não está configurado
      if (user && !isConfigured) {
        try {
          const config = await flowStorageService.getFlowConfig(user.uid);

          // Só abre o modal se não há configuração ou se está incompleta
          if (!config || !isConfigurationComplete(config)) {
            setShowConfigModal(true);
          }
        } catch (error) {
          console.error('Erro ao verificar configuração:', error);
          // Só abre o modal em caso de erro se realmente não estiver configurado
          if (!isConfigured) {
            setShowConfigModal(true);
          }
        }
      }
    };

    // Só executa a verificação se não estiver configurado
    if (!isConfigured && user) {
      checkConfiguration();
    }
  }, [isConfigured, user, setShowConfigModal, isConfigurationComplete]);
  
  // Função para carregar sessões recentes do usuário
  const loadUserRecentSessions = async () => {
    try {
      setLoading(true);
      setError(null);
      const sessions = await loadUserSessions();
      setRecentSessions(sessions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar sessões';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  // Função para criar nova sessão
  const handleNewSession = async () => {
    try {
      setLoading(true);
      const newSession = await createSession();
      // Navegar para a página de chat com a nova sessão
      navigate(`/chat/${newSession.id}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar sessão';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Função para excluir sessão
  const handleDeleteSession = async (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Evita que o clique no botão de excluir abra a sessão

    if (!confirm('Tem certeza que deseja excluir esta sessão? Esta ação não pode ser desfeita.')) {
      return;
    }

    try {
      await flowStorageService.deleteSession(sessionId);
      // Recarregar sessões após exclusão
      loadUserRecentSessions();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao excluir sessão';
      setError(errorMessage);
    }
  };

  // Função para iniciar edição do nome
  const startEditingName = (sessionId: string, currentName: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Evita que o clique abra a sessão
    setEditingSessionId(sessionId);
    setEditingName(currentName || `Sessão ${sessionId.substring(0, 8)}...`);
  };

  // Função para salvar nome editado
  const saveEditedName = async (sessionId: string, event?: React.KeyboardEvent) => {
    if (event && event.key !== 'Enter') return;

    try {
      await renameSession(sessionId, editingName.trim());
      setEditingSessionId(null);
      setEditingName('');
      // Recarregar sessões para mostrar o nome atualizado
      loadUserRecentSessions();
    } catch (err) {
      console.error('Erro ao renomear sessão:', err);
      setError('Erro ao renomear sessão');
    }
  };

  // Função para cancelar edição do nome
  const cancelEditingName = () => {
    setEditingSessionId(null);
    setEditingName('');
  };
  
  // Função para obter nome amigável do modelo
  const getModelDisplayName = (modelId: string): string => {
    if (!modelId || modelId.includes('unknown')) {
      return 'GPT-4o Mini (padrão)';
    }

    // Mapear IDs de modelos para nomes amigáveis
    const modelNames: { [key: string]: string } = {
      'gpt-4o': 'GPT-4o',
      'gpt-4o-mini': 'GPT-4o Mini',
      'gpt-4.1': 'GPT-4.1',
      'o3-mini': 'O3 Mini',
      'gemini-2.0-flash': 'Gemini 2.0 Flash',
      'gemini-2.5-pro': 'Gemini 2.5 Pro',
      'anthropic.claude-37-sonnet': 'Claude 3.7 Sonnet',
      'amazon.nova-lite': 'Amazon Nova Lite',
      'amazon.nova-micro': 'Amazon Nova Micro',
      'amazon.nova-pro': 'Amazon Nova Pro',
      'meta.llama3-70b-instruct': 'Llama 3 70B',
      'DeepSeek-R1': 'DeepSeek R1'
    };

    return modelNames[modelId] || modelId;
  };

  // Formata data para exibição
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  return (
    <div className="min-h-screen bg-theme-primary page-with-fixed-header">
      <Header />

      <main className="max-w-6xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8 flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-theme-primary">
              Bem-vindo, {user?.displayName || 'Usuário'}
            </h1>
            <p className="text-theme-secondary mt-1">
              Plataforma de aprendizado adaptativo Flow AdaptEd
            </p>
          </div>

          <Button
            variant="ghost"
            onClick={handleNewSession}
            disabled={loading || !isConfigured}
            className="p-2 rounded-lg bg-theme-secondary/50 border border-theme hover:bg-theme-tertiary transition-all duration-200 flex items-center space-x-2"
          >
            <svg
              className="w-5 h-5 text-theme-primary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="text-sm font-medium text-theme-primary">Nova conversa</span>
          </Button>
        </div>
      
      {/* Mensagens de erro */}
      {(error || flowError) && (
        <Alert 
          variant="error" 
          className="mb-6"
          title="Erro"
          onClose={() => setError(null)}
        >
          {error || flowError}
        </Alert>
      )}
      
      {/* Seção principal */}
      <div>
        {/* Sessões recentes */}
        {loading ? (
          <div className="flex justify-center items-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-theme-focus"></div>
          </div>
        ) : recentSessions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentSessions.map((session) => (
              <div key={session.id} onClick={() => navigate(`/chat/${session.id}`)}>
                <Card className="h-48 cursor-pointer focus-ring card-theme hover:bg-theme-tertiary transition-colors relative" isHoverable>
                  <div className="p-4 h-full flex flex-col">
                    {/* Header com título e botões */}
                    <div className="flex justify-between items-start mb-2">
                      {editingSessionId === session.id ? (
                        <div className="flex-1 flex items-center space-x-2">
                          <input
                            type="text"
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') saveEditedName(session.id, e);
                              if (e.key === 'Escape') cancelEditingName();
                            }}
                            className="flex-1 text-sm font-bold text-theme-primary bg-theme-primary border border-theme rounded px-2 py-1"
                            autoFocus
                          />
                          <button
                            onClick={() => saveEditedName(session.id)}
                            className="text-green-600 hover:text-green-700 transition-colors p-1"
                            title="Salvar"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </button>
                          <button
                            onClick={cancelEditingName}
                            className="text-red-600 hover:text-red-700 transition-colors p-1"
                            title="Cancelar"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      ) : (
                        <div className="flex-1 flex items-center space-x-2">
                          <h3 className="font-bold text-theme-primary">
                            {session.name || `Sessão ${session.id.substring(0, 8)}...`}
                          </h3>
                          <button
                            onClick={(e) => startEditingName(session.id, session.name || '', e)}
                            className="text-theme-muted hover:text-theme-primary transition-colors p-1"
                            title="Editar nome"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                          </button>
                        </div>
                      )}

                      <button
                        onClick={(e) => handleDeleteSession(session.id, e)}
                        className="text-theme-muted hover:text-red-500 transition-colors p-1 rounded hover:bg-theme-tertiary ml-2"
                        title="Excluir sessão"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>

                    {/* Informações da sessão */}
                    <div className="flex-1 text-sm text-theme-secondary space-y-1">
                      <p><span className="font-medium">Modelo:</span> {getModelDisplayName(session.modelId)}</p>
                      <p><span className="font-medium">Criado em:</span> {formatDate(session.createdAt)}</p>
                      <p><span className="font-medium">Último uso:</span> {formatDate(session.lastUsedAt)}</p>
                    </div>

                    {/* Status da sessão */}
                    <div className="mt-2 text-xs text-theme-muted">
                      {session.messages.length > 0
                        ? `${session.messages.length} mensagens`
                        : 'Sessão vazia'}
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-theme-secondary rounded-lg p-8 text-center">
            <p className="text-theme-secondary">
              {isConfigured
                ? 'Nenhuma sessão encontrada. Use o botão "Nova conversa" para começar.'
                : 'Configure as credenciais do Flow para começar a usar o chat.'}
            </p>
            {!isConfigured && (
              <Button
                variant="primary"
                className="mt-4"
                onClick={() => setShowConfigModal(true)}
              >
                Configurar Flow
              </Button>
            )}
          </div>
        )}
      </div>
      </main>

      {/* Modal de configuração do Flow */}
      <FlowConfigModal
        isOpen={showConfigModal}
        onClose={() => setShowConfigModal(false)}
        onSuccess={() => {
          // Recarregar sessões após configuração bem-sucedida
          loadUserRecentSessions();
        }}
      />
    </div>
  );
};

export default HomePage;
