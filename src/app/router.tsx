/**
 * Configuração de rotas da aplicação com React Router e lazy loading
 */
import React, { lazy, Suspense } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@/components/AuthProvider';

// Componente de fallback durante o carregamento
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Lazy loading dos componentes de página
const LoginPage = lazy(() => import('@/features/auth/pages/LoginPage'));
const HomePage = lazy(() => import('@/features/flow/pages/HomePage'));
const ChatPage = lazy(() => import('@/features/flow/pages/ChatPage'));
const ConfigPage = lazy(() => import('@/features/settings/pages/ConfigPage'));
const NotFoundPage = lazy(() => import('@/components/NotFoundPage'));

// Rota protegida que verifica autenticação
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();

  console.log("🔒 [ProtectedRoute] Estado:", {
    user: !!user,
    loading,
    currentPath: window.location.pathname
  });

  if (loading) {
    console.log("⏳ [ProtectedRoute] Carregando...");
    return <LoadingFallback />;
  }

  if (!user) {
    console.log("❌ [ProtectedRoute] Usuário não autenticado, redirecionando para /login");
    return <Navigate to="/login" replace />;
  }

  console.log("✅ [ProtectedRoute] Usuário autenticado, renderizando conteúdo");
  return <>{children}</>;
};

// Rota pública que redireciona usuários autenticados
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return <LoadingFallback />;
  }
  
  if (user) {
    return <Navigate to="/" replace />;
  }
  
  return <>{children}</>;
};

// Componente principal de rotas
export const AppRouter: React.FC = () => {
  React.useEffect(() => {
    console.log("🚀 [AppRouter] Inicializado, rota atual:", window.location.pathname);

    // Detectar mudanças de rota
    const handlePopState = () => {
      console.log("🔄 [AppRouter] Mudança de rota detectada:", window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  return (
    <BrowserRouter>
      <Suspense fallback={<LoadingFallback />}>
        <Routes>
          {/* Rotas públicas */}
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />

          {/* Rotas protegidas */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <HomePage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/chat/:sessionId?"
            element={
              <ProtectedRoute>
                <ChatPage />
              </ProtectedRoute>
            }
          />

          <Route
            path="/config"
            element={
              <ProtectedRoute>
                <ConfigPage />
              </ProtectedRoute>
            }
          />

          {/* Rota 404 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </BrowserRouter>
  );
};

export default AppRouter;
